{% extends 'base.html' %} {% load static %} {% block content_map %}
<!-- MAP SHOW-->
<div id="map"></div>

<!--begin::Drawers-->
  <!--begin::Activities drawer-->
  <div id="kt_activities" class="bg-body" data-kt-drawer="true" data-kt-drawer-name="activities"
    data-kt-drawer-activate="true" data-kt-drawer-overlay="true" data-kt-drawer-width="{default:'400px', 'lg': '1000px'}"
    data-kt-drawer-direction="end" data-kt-drawer-toggle="#kt_activities_toggle"
    data-kt-drawer-close="#kt_activities_close">
    <div class="card shadow-none border-0 rounded-0">
      <!--begin::Header-->
      <div class="card-header" id="kt_activities_header">
        <h3 class="card-title fw-bold text-dark">ค้นหาข้อมูลโฉนดที่ดิน</h3>
        <div class="card-toolbar">
          <button type="button" class="btn btn-sm btn-icon btn-active-light-primary me-n5" id="kt_activities_close">
            <!--begin::Svg Icon | path: icons/duotune/arrows/arr061.svg-->
            <span class="svg-icon svg-icon-1">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect opacity="0.5" x="6" y="17.3137" width="16" height="2" rx="1" transform="rotate(-45 6 17.3137)"
                  fill="currentColor" />
                <rect x="7.41422" y="6" width="16" height="2" rx="1" transform="rotate(45 7.41422 6)"
                  fill="currentColor" />
              </svg>
            </span>
            <!--end::Svg Icon-->
          </button>
        </div>
      </div>
      <!--end::Header-->
      <!--begin::Body-->
      <div class="card-body position-relative" id="kt_activities_body">
        <!--begin::Search Form-->
        <div class="mb-7">
          <!--begin::Search Input-->
          <div class="position-relative mb-5">
            <span class="svg-icon svg-icon-1 svg-icon-gray-500 position-absolute top-50 translate-middle-y ms-4">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect opacity="0.5" x="17.0365" y="15.1223" width="8.15546" height="2" rx="1" transform="rotate(45 17.0365 15.1223)" fill="currentColor"/>
                <path d="M11 19C6.55556 19 3 15.4444 3 11C3 6.55556 6.55556 3 11 3C15.4444 3 19 6.55556 19 11C19 15.4444 15.4444 19 11 19ZM11 5C7.53333 5 5 7.53333 5 11C5 14.4667 7.53333 17 11 17C14.4667 17 17 14.4667 17 11C17 7.53333 14.4667 5 11 5Z" fill="currentColor"/>
              </svg>
            </span>
            <input type="text" class="form-control form-control-solid ps-13" placeholder="ค้นหาเลขที่โฉนด หรือ เลขที่ดิน..." id="search_parcel_input"/>
          </div>
          <!--end::Search Input-->

          <!--begin::Filter Options-->
          <div class="mb-5">
            <label class="form-label fw-bold fs-6 text-gray-700">โฉนดที่ดิน</label>
            <div class="d-flex flex-wrap gap-5">
              <!--begin::Checkbox Private-->
              <div class="form-check form-check-custom form-check-solid">
                <input class="form-check-input" type="checkbox" value="private" id="filter_private" checked/>
                <label class="form-check-label fw-semibold text-gray-700" for="filter_private">
                  ทรัพย์สินในพระองค์
                </label>
              </div>
              <!--end::Checkbox Private-->

              <!--begin::Checkbox Prakang-->
              <div class="form-check form-check-custom form-check-solid">
                <input class="form-check-input" type="checkbox" value="prakang" id="filter_prakang" checked/>
                <label class="form-check-label fw-semibold text-gray-700" for="filter_prakang">
                  พระคลังข้างที่
                </label>
              </div>
              <!--end::Checkbox Prakang-->

              <!--begin::Checkbox CPB-->
              <div class="form-check form-check-custom form-check-solid">
                <input class="form-check-input" type="checkbox" value="cpb" id="filter_cpb" checked/>
                <label class="form-check-label fw-semibold text-gray-700" for="filter_cpb">
                  สำนักงานทรัพย์สินฯ
                </label>
              </div>
              <!--end::Checkbox CPB-->
            </div>
          </div>
          <!--end::Filter Options-->

          <!--begin::Filter Options-->
          <div class="mb-5">
            <label class="form-label fw-bold fs-6 text-gray-700">ประเภทโฉนดที่ดิน</label>
            <div class="d-flex flex-wrap gap-5">
              <!--begin::Checkbox Private-->
              <div class="form-check form-check-custom form-check-solid">
                <input class="form-check-input" type="checkbox" value="private" id="filter_private" checked/>
                <label class="form-check-label fw-semibold text-gray-700" for="filter_private">
                  น.ส.4
                </label>
              </div>
              <!--end::Checkbox Private-->

              <!--begin::Checkbox Prakang-->
              <div class="form-check form-check-custom form-check-solid">
                <input class="form-check-input" type="checkbox" value="prakang" id="filter_prakang" checked/>
                <label class="form-check-label fw-semibold text-gray-700" for="filter_prakang">
                  น.ส.3
                </label>
              </div>
              <!--end::Checkbox Prakang-->

              <!--begin::Checkbox CPB-->
              <div class="form-check form-check-custom form-check-solid">
                <input class="form-check-input" type="checkbox" value="cpb" id="filter_cpb" checked/>
                <label class="form-check-label fw-semibold text-gray-700" for="filter_cpb">
                  ตราจอง
                </label>
              </div>
              <!--end::Checkbox CPB-->
            </div>
          </div>
          <!--end::Filter Options-->

          <!--begin::Filter Options-->
          <div class="mb-5">
            <label class="form-label fw-bold fs-6 text-gray-700">ประเภทโฉนดที่ดิน</label>
            <div class="d-flex flex-wrap gap-5">
              <!--begin::Checkbox Private-->
              <div class="form-check form-check-custom form-check-solid">
                <input class="form-check-input" type="checkbox" value="private" id="filter_private" checked/>
                <label class="form-check-label fw-semibold text-gray-700" for="filter_private">
                  น.ส.4
                </label>
              </div>
              <!--end::Checkbox Private-->

              <!--begin::Checkbox Prakang-->
              <div class="form-check form-check-custom form-check-solid">
                <input class="form-check-input" type="checkbox" value="prakang" id="filter_prakang" checked/>
                <label class="form-check-label fw-semibold text-gray-700" for="filter_prakang">
                  น.ส.3
                </label>
              </div>
              <!--end::Checkbox Prakang-->

              <!--begin::Checkbox CPB-->
              <div class="form-check form-check-custom form-check-solid">
                <input class="form-check-input" type="checkbox" value="cpb" id="filter_cpb" checked/>
                <label class="form-check-label fw-semibold text-gray-700" for="filter_cpb">
                  ตราจอง
                </label>
              </div>
              <!--end::Checkbox CPB-->
            </div>
          </div>
          <!--end::Filter Options-->

          <!--begin::Search Button-->
          <div class="d-flex justify-content-end">
            <button type="button" class="btn btn-primary" id="search_parcel_btn">
              <span class="svg-icon svg-icon-2">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <rect opacity="0.5" x="17.0365" y="15.1223" width="8.15546" height="2" rx="1" transform="rotate(45 17.0365 15.1223)" fill="currentColor"/>
                  <path d="M11 19C6.55556 19 3 15.4444 3 11C3 6.55556 6.55556 3 11 3C15.4444 3 19 6.55556 19 11C19 15.4444 15.4444 19 11 19ZM11 5C7.53333 5 5 7.53333 5 11C5 14.4667 7.53333 17 11 17C14.4667 17 17 14.4667 17 11C17 7.53333 14.4667 5 11 5Z" fill="currentColor"/>
                </svg>
              </span>
              ค้นหา
            </button>
          </div>
          <!--end::Search Button-->
        </div>
        <!--end::Search Form-->

        <!--begin::Content-->
        <div id="kt_activities_scroll" class="position-relative scroll-y me-n5 pe-5" data-kt-scroll="true"
          data-kt-scroll-height="auto" data-kt-scroll-wrappers="#kt_activities_body"
          data-kt-scroll-dependencies="#kt_activities_header, #kt_activities_footer" data-kt-scroll-offset="5px">

          <!--begin::Results Table-->
          <div class="table-responsive">
            <table class="table table-row-dashed table-row-gray-300 align-middle gs-0 gy-4" id="parcel_results_table">
              <!--begin::Table head-->
              <thead>
                <tr class="fw-bold text-muted">
                  <th class="min-w-200px">โฉนดที่ดิน</th>
                  <th class="min-w-200px">เลขที่ดิน</th>
                  <th class="min-w-200px">เลขระวาง</th>
                  <th class="min-w-100px">เนื้อที่โฉนด</th>
                  <th class="min-w-200px text-end">จัดการ</th>
                </tr>
              </thead>
              <!--end::Table head-->

              <!--begin::Table body-->
              <tbody id="parcel_results_tbody">
                <!--begin::Sample Row (will be replaced by dynamic content)-->
                <tr>
                  <td>
                    <div class="d-flex align-items-center">
                      <div class="symbol symbol-45px me-5">
                        <div class="symbol-label bg-light-primary">
                          <span class="svg-icon svg-icon-2x svg-icon-primary">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <path opacity="0.3" d="M22 8H8L12 4H19C19.6 4 20.2 4.39999 20.5 4.89999L22 8ZM3.5 19.1C3.8 19.7 4.4 20 5 20H12L16 16H2L3.5 19.1ZM19.1 20.5C19.7 20.2 20 19.6 20 19V12L16 8V22L19.1 20.5ZM4.9 3.5C4.3 3.8 4 4.4 4 5V12L8 16V2L4.9 3.5Z" fill="currentColor"/>
                              <path d="M22 8L20 12L16 8H22ZM8 16L4 12L2 16H8ZM16 16L12 20L16 22V16ZM8 8L12 4L8 2V8Z" fill="currentColor"/>
                            </svg>
                          </span>
                        </div>
                      </div>
                      <div class="d-flex justify-content-start flex-column">
                        <span class="text-dark fw-bold text-hover-primary fs-6">6653</span>
                        <span class="text-muted fw-semibold text-muted d-block fs-7">Private</span>
                      </div>
                    </div>
                  </td>
                  <td>
                    <span class="text-dark fw-bold d-block fs-6">123/45</span>
                  </td>
                  <td>
                    <span class="text-dark fw-bold d-block fs-6">67</span>
                  </td>
                  <td>
                    <span class="text-dark fw-bold d-block fs-6">2-1-50 ไร่</span>
                  </td>
                  <td class="text-end">
                    <div class="d-flex justify-content-end flex-shrink-0">
                      <button class="btn btn-icon btn-bg-light btn-active-color-primary btn-sm me-1" data-bs-toggle="tooltip" title="ดูรายละเอียด">
                        <span class="svg-icon svg-icon-3">
                          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M17.5 11H6.5C4 11 2 9 2 6.5C2 4 4 2 6.5 2H17.5C20 2 22 4 22 6.5C22 9 20 11 17.5 11ZM15 6.5C15 7.9 16.1 9 17.5 9C18.9 9 20 7.9 20 6.5C20 5.1 18.9 4 17.5 4C16.1 4 15 5.1 15 6.5Z" fill="currentColor"/>
                            <path opacity="0.3" d="M17.5 22H6.5C4 22 2 20 2 17.5C2 15 4 13 6.5 13H17.5C20 13 22 15 22 17.5C22 20 20 22 17.5 22ZM4 17.5C4 18.9 5.1 20 6.5 20C7.9 20 9 18.9 9 17.5C9 16.1 7.9 15 6.5 15C5.1 15 4 16.1 4 17.5Z" fill="currentColor"/>
                          </svg>
                        </span>
                      </button>
                      <button class="btn btn-icon btn-bg-light btn-active-color-primary btn-sm" data-bs-toggle="tooltip" title="แสดงบนแผนที่">
                        <span class="svg-icon svg-icon-3">
                          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path opacity="0.3" d="M18.4 5.59998C21.9 9.09998 21.9 14.8 18.4 18.3C14.9 21.8 9.2 21.8 5.7 18.3L18.4 5.59998Z" fill="currentColor"/>
                            <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 12C21 13.1 20.1 14 19 14C17.9 14 17 13.1 17 12C17 10.9 17.9 10 19 10C20.1 10 21 10.9 21 12ZM7 12C7 13.1 6.1 14 5 14C3.9 14 3 13.1 3 12C3 10.9 3.9 10 5 10C6.1 10 7 10.9 7 12ZM12 17C10.9 17 10 17.9 10 19C10 20.1 10.9 21 12 21C13.1 21 14 20.1 14 19C14 17.9 13.1 17 12 17Z" fill="currentColor"/>
                          </svg>
                        </span>
                      </button>
                    </div>
                  </td>
                </tr>
                
                <!--end::Sample Row-->
              </tbody>
              <!--end::Table body-->
            </table>
          </div>
          <!--end::Results Table-->

          <!--begin::Empty State-->
          <div class="text-center py-10 d-none" id="empty_state">
            <div class="mb-7">
              <span class="svg-icon svg-icon-5x svg-icon-muted">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <rect opacity="0.5" x="17.0365" y="15.1223" width="8.15546" height="2" rx="1" transform="rotate(45 17.0365 15.1223)" fill="currentColor"/>
                  <path d="M11 19C6.55556 19 3 15.4444 3 11C3 6.55556 6.55556 3 11 3C15.4444 3 19 6.55556 19 11C19 15.4444 15.4444 19 11 19ZM11 5C7.53333 5 5 7.53333 5 11C5 14.4667 7.53333 17 11 17C14.4667 17 17 14.4667 17 11C17 7.53333 14.4667 5 11 5Z" fill="currentColor"/>
                </svg>
              </span>
            </div>
            <h5 class="text-gray-600 fw-semibold mb-2">ไม่พบข้อมูลโฉนดที่ดิน</h5>
            <span class="text-gray-400 fw-semibold fs-6">กรุณาลองค้นหาด้วยคำค้นอื่น หรือ เปลี่ยนตัวกรองการค้นหา</span>
          </div>
          <!--end::Empty State-->
            
          </div>
          <!--end::Timeline items-->
        </div>
        <!--end::Content-->
      </div>
      <!--end::Body-->
      
    </div>
  </div>
  <!--end::Activities drawer-->
  <!--end::Drawers-->

  
{% comment %}
<div class="xUNLkc">
  <button type="button" class="btn btn-outline-primary btn-sm">Primary</button>
  <button type="button" class="btn btn-primary btn-sm">
    งานออก <span class="badge bg-danger">1</span>
  </button>
</div>
{% endcomment %}

<!-- End MAP SHOW-->

<!--begin::Engage toolbar-->
<div
  class="engage-toolbar d-flex position-fixed px-5 fw-bold zindex-2 top-50 end-0 transform-90 mt-5 mt-lg-20 gap-2"
>
  <!--begin::Demos drawer toggle-->
  <button
    id="description_parcel"
    class="engage-demos-toggle engage-btn btn shadow-sm fs-6 px-4 rounded-top-0"
    title="แสดงรายละเอียดโฉนดที่ดิน"
    data-bs-toggle="tooltip"
    data-bs-placement="right"
    data-bs-dismiss="click"
    data-bs-trigger="hover"
  >
    <span id="kt_engage_demos_label">รายละเอียด</span>
  </button>
  <!--end::Demos drawer toggle-->
  <!--begin::Help drawer toggle-->
  {% comment %}
  <button
    id="kt_help_toggle"
    class="engage-help-toggle btn engage-btn shadow-sm px-5 rounded-top-0"
    title="Learn & Get Inspired"
    data-bs-toggle="tooltip"
    data-bs-placement="left"
    data-bs-dismiss="click"
    data-bs-trigger="hover"
  >
    Help
  </button>
  {% endcomment %}
  <!--end::Help drawer toggle-->
</div>
<!--end::Engage toolbar-->

<!--begin::Engage drawers-->
<!--begin::description_รายละเอียดโฉนดและสรุปข้อมูล drawer-->
<div
  id="description_parcel_detail"
  class="bg-body"
  data-kt-drawer="true"
  data-kt-drawer-name="description"
  data-kt-drawer-activate="true"
  data-kt-drawer-overlay="false"
  data-kt-drawer-width="{default:'350px', 'lg': '475px'}"
  data-kt-drawer-direction="end"
  data-kt-drawer-toggle="#description_parcel"
  data-kt-drawer-close="#description_parcel_detail"
>
  <!--begin::Card-->
  <div class="card shadow-none rounded-0 w-100">
    <!--begin::Header-->
    <div class="card-header" id="kt_engage_demos_header">
      <h3 class="card-title fw-bold text-gray-700">โฉนดที่ดินเลขที่ 6653</h3>
      <div class="card-toolbar">
        <button
          type="button"
          class="btn btn-sm btn-icon btn-active-color-primary h-40px w-40px me-n6"
          id="kt_engage_demos_close"
        >
          <!--begin::Svg Icon | path: icons/duotune/arrows/arr061.svg-->
          <span class="svg-icon svg-icon-2">
            <svg
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <rect
                opacity="0.5"
                x="6"
                y="17.3137"
                width="16"
                height="2"
                rx="1"
                transform="rotate(-45 6 17.3137)"
                fill="currentColor"
              />
              <rect
                x="7.41422"
                y="6"
                width="16"
                height="2"
                rx="1"
                transform="rotate(45 7.41422 6)"
                fill="currentColor"
              />
            </svg>
          </span>
          <!--end::Svg Icon-->
        </button>
      </div>
    </div>
    <!--end::Header-->
    <!--begin::Body-->
    <div class="card-body" id="kt_engage_demos_body">
      <!--begin::Content-->
      <div
        id="kt_explore_scroll"
        class="scroll-y me-n5 pe-5"
        data-kt-scroll="true"
        data-kt-scroll-height="auto"
        data-kt-scroll-wrappers="#kt_engage_demos_body"
        data-kt-scroll-dependencies="#kt_engage_demos_header"
        data-kt-scroll-offset="5px"
      >
        <!--begin::Wrapper-->
        <div class="mb-0">
          <!--begin::Heading-->
          <div class="mb-7">
            <div class="d-flex flex-stack">
              <h3 class="mb-0">สรุปข้อมูลภายในโฉนด</h3>
              {% comment %}
              <a href="#" class="fw-semibold" target="_blank">License FAQs</a>
              {% endcomment %}
            </div>
          </div>
          <!--end::Heading-->
          <!--begin::License-->
          <div
            class="card card-xxl-stretch mb-5 mb-xl-8"
            style="background-color: #f7d9e3"
          >
            <!--begin::Body-->
            <div class="card-body d-flex flex-column">
              <!--begin::Wrapper-->
              <div class="d-flex flex-column mb-7">
                <!--begin::Title-->
                <a href="#" class="text-dark text-hover-primary fw-bolder fs-3"
                  >กรรมสิทธิ์ที่ดิน</a
                >
                <!--end::Title-->
              </div>
              <!--end::Wrapper-->
              <!--begin::Row-->
              <div class="row g-0">
                <!--begin::Col-->
                <div class="col-6">
                  <div class="d-flex align-items-center mb-9 me-2">
                    <!--begin::Symbol-->
                    <div class="symbol symbol-40px me-3">
                      <div class="symbol-label bg-white bg-opacity-50">
                        <!--begin::Svg Icon | path: icons/duotune/abstract/abs043.svg-->
                        <span class="svg-icon svg-icon-1 svg-icon-dark">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="24"
                            height="24"
                            viewBox="0 0 24 24"
                            fill="none"
                          >
                            <path
                              opacity="0.3"
                              d="M22 8H8L12 4H19C19.6 4 20.2 4.39999 20.5 4.89999L22 8ZM3.5 19.1C3.8 19.7 4.4 20 5 20H12L16 16H2L3.5 19.1ZM19.1 20.5C19.7 20.2 20 19.6 20 19V12L16 8V22L19.1 20.5ZM4.9 3.5C4.3 3.8 4 4.4 4 5V12L8 16V2L4.9 3.5Z"
                              fill="currentColor"
                            ></path>
                            <path
                              d="M22 8L20 12L16 8H22ZM8 16L4 12L2 16H8ZM16 16L12 20L16 22V16ZM8 8L12 4L8 2V8Z"
                              fill="currentColor"
                            ></path>
                          </svg>
                        </span>
                        <!--end::Svg Icon-->
                      </div>
                    </div>
                    <!--end::Symbol-->
                    <!--begin::Title-->
                    <div>
                      <div class="fs-5 text-dark fw-bolder lh-1">50 แปลง</div>
                      <div class="fs-7 text-gray-600 fw-bold">สัญญาเช่า</div>
                    </div>
                    <!--end::Title-->
                  </div>
                </div>
                <!--end::Col-->
                <!--begin::Col-->
                <div class="col-6">
                  <div class="d-flex align-items-center mb-9 ms-2">
                    <!--begin::Symbol-->
                    <div class="symbol symbol-40px me-3">
                      <div class="symbol-label bg-white bg-opacity-50">
                        <!--begin::Svg Icon | path: icons/duotune/abstract/abs046.svg-->
                        <span class="svg-icon svg-icon-1 svg-icon-dark">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="24"
                            height="24"
                            viewBox="0 0 24 24"
                            fill="none"
                          >
                            <path
                              d="M8 22C7.4 22 7 21.6 7 21V9C7 8.4 7.4 8 8 8C8.6 8 9 8.4 9 9V21C9 21.6 8.6 22 8 22Z"
                              fill="currentColor"
                            ></path>
                            <path
                              opacity="0.3"
                              d="M4 15C3.4 15 3 14.6 3 14V6C3 5.4 3.4 5 4 5C4.6 5 5 5.4 5 6V14C5 14.6 4.6 15 4 15ZM13 19V3C13 2.4 12.6 2 12 2C11.4 2 11 2.4 11 3V19C11 19.6 11.4 20 12 20C12.6 20 13 19.6 13 19ZM17 16V5C17 4.4 16.6 4 16 4C15.4 4 15 4.4 15 5V16C15 16.6 15.4 17 16 17C16.6 17 17 16.6 17 16ZM21 18V10C21 9.4 20.6 9 20 9C19.4 9 19 9.4 19 10V18C19 18.6 19.4 19 20 19C20.6 19 21 18.6 21 18Z"
                              fill="currentColor"
                            ></path>
                          </svg>
                        </span>
                        <!--end::Svg Icon-->
                      </div>
                    </div>
                    <!--end::Symbol-->
                    <!--begin::Title-->
                    <div>
                      <div class="fs-5 text-dark fw-bolder lh-1">50 อัตรา</div>
                      <div class="fs-7 text-gray-600 fw-bold">อัตรา</div>
                    </div>
                    <!--end::Title-->
                  </div>
                </div>
                <!--end::Col-->
                <!--begin::Col-->
                <div class="col-6">
                  <div class="d-flex align-items-center me-2">
                    <!--begin::Symbol-->
                    <div class="symbol symbol-40px me-3">
                      <div class="symbol-label bg-white bg-opacity-50">
                        <!--begin::Svg Icon | path: icons/duotune/abstract/abs022.svg-->
                        <span class="svg-icon svg-icon-1 svg-icon-dark">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="24"
                            height="24"
                            viewBox="0 0 24 24"
                            fill="none"
                          >
                            <path
                              opacity="0.3"
                              d="M11.425 7.325C12.925 5.825 15.225 5.825 16.725 7.325C18.225 8.825 18.225 11.125 16.725 12.625C15.225 14.125 12.925 14.125 11.425 12.625C9.92501 11.225 9.92501 8.825 11.425 7.325ZM8.42501 4.325C5.32501 7.425 5.32501 12.525 8.42501 15.625C11.525 18.725 16.625 18.725 19.725 15.625C22.825 12.525 22.825 7.425 19.725 4.325C16.525 1.225 11.525 1.225 8.42501 4.325Z"
                              fill="currentColor"
                            ></path>
                            <path
                              d="M11.325 17.525C10.025 18.025 8.425 17.725 7.325 16.725C5.825 15.225 5.825 12.925 7.325 11.425C8.825 9.92498 11.125 9.92498 12.625 11.425C13.225 12.025 13.625 12.925 13.725 13.725C14.825 13.825 15.925 13.525 16.725 12.625C17.125 12.225 17.425 11.825 17.525 11.325C17.125 10.225 16.525 9.22498 15.625 8.42498C12.525 5.32498 7.425 5.32498 4.325 8.42498C1.225 11.525 1.225 16.625 4.325 19.725C7.425 22.825 12.525 22.825 15.625 19.725C16.325 19.025 16.925 18.225 17.225 17.325C15.425 18.125 13.225 18.225 11.325 17.525Z"
                              fill="currentColor"
                            ></path>
                          </svg>
                        </span>
                        <!--end::Svg Icon-->
                      </div>
                    </div>
                    <!--end::Symbol-->
                    <!--begin::Title-->
                    <div>
                      <div class="fs-5 text-dark fw-bolder lh-1">100 หลัง</div>
                      <div class="fs-7 text-gray-600 fw-bold">
                        สิ่งปลูกสร้าง
                      </div>
                    </div>
                    <!--end::Title-->
                  </div>
                </div>
                <!--end::Col-->
                <!--begin::Col-->
                <div class="col-6">
                  <div class="d-flex align-items-center ms-2">
                    <!--begin::Symbol-->
                    <div class="symbol symbol-40px me-3">
                      <div class="symbol-label bg-white bg-opacity-50">
                        <!--begin::Svg Icon | path: icons/duotune/abstract/abs045.svg-->
                        <span class="svg-icon svg-icon-1 svg-icon-dark">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="24"
                            height="24"
                            viewBox="0 0 24 24"
                            fill="none"
                          >
                            <path
                              d="M2 11.7127L10 14.1127L22 11.7127L14 9.31274L2 11.7127Z"
                              fill="currentColor"
                            ></path>
                            <path
                              opacity="0.3"
                              d="M20.9 7.91274L2 11.7127V6.81275C2 6.11275 2.50001 5.61274 3.10001 5.51274L20.6 2.01274C21.3 1.91274 22 2.41273 22 3.11273V6.61273C22 7.21273 21.5 7.81274 20.9 7.91274ZM22 16.6127V11.7127L3.10001 15.5127C2.50001 15.6127 2 16.2127 2 16.8127V20.3127C2 21.0127 2.69999 21.6128 3.39999 21.4128L20.9 17.9128C21.5 17.8128 22 17.2127 22 16.6127Z"
                              fill="currentColor"
                            ></path>
                          </svg>
                        </span>
                        <!--end::Svg Icon-->
                      </div>
                    </div>
                    <!--end::Symbol-->
                    <!--begin::Title-->
                    <div>
                      <div class="fs-5 text-dark fw-bolder lh-1">
                        30,000 ตารางวา
                      </div>
                      <div class="fs-7 text-gray-600 fw-bold">
                        พื้นที่จัดประโยชน์
                      </div>
                    </div>
                    <!--end::Title-->
                  </div>
                </div>
                <!--end::Col-->
              </div>
              <!--end::Row-->
            </div>
          </div>

          <div
            class="card card-xxl-stretch mb-5 mb-xl-8"
            style="background-color: #cbf0f4"
          >
            <!--begin::Body-->
            <div class="card-body d-flex flex-column">
              <!--begin::Wrapper-->
              <div class="d-flex flex-column mb-7">
                <!--begin::Title-->
                <a href="#" class="text-dark text-hover-primary fw-bolder fs-3"
                  >สิ่งปลูกสร้าง</a
                >
                <!--end::Title-->
              </div>
              <!--end::Wrapper-->
              <!--begin::Row-->
              <div class="row g-0">
                <!--begin::Col-->
                <div class="col-6">
                  <div class="d-flex align-items-center mb-9 me-2">
                    <!--begin::Symbol-->
                    <div class="symbol symbol-40px me-3">
                      <div class="symbol-label bg-white bg-opacity-50">
                        <!--begin::Svg Icon | path: icons/duotune/abstract/abs043.svg-->
                        <span class="svg-icon svg-icon-1 svg-icon-dark">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="24"
                            height="24"
                            viewBox="0 0 24 24"
                            fill="none"
                          >
                            <path
                              opacity="0.3"
                              d="M22 8H8L12 4H19C19.6 4 20.2 4.39999 20.5 4.89999L22 8ZM3.5 19.1C3.8 19.7 4.4 20 5 20H12L16 16H2L3.5 19.1ZM19.1 20.5C19.7 20.2 20 19.6 20 19V12L16 8V22L19.1 20.5ZM4.9 3.5C4.3 3.8 4 4.4 4 5V12L8 16V2L4.9 3.5Z"
                              fill="currentColor"
                            ></path>
                            <path
                              d="M22 8L20 12L16 8H22ZM8 16L4 12L2 16H8ZM16 16L12 20L16 22V16ZM8 8L12 4L8 2V8Z"
                              fill="currentColor"
                            ></path>
                          </svg>
                        </span>
                        <!--end::Svg Icon-->
                      </div>
                    </div>
                    <!--end::Symbol-->
                    <!--begin::Title-->
                    <div>
                      <div class="fs-5 text-dark fw-bolder lh-1">5 หลัง</div>
                      <div class="fs-7 text-gray-600 fw-bold">
                        บ้านตึกชั้นเดียว
                      </div>
                    </div>
                    <!--end::Title-->
                  </div>
                </div>
                <!--end::Col-->
                <!--begin::Col-->
                <div class="col-6">
                  <div class="d-flex align-items-center mb-9 me-2">
                    <!--begin::Symbol-->
                    <div class="symbol symbol-40px me-3">
                      <div class="symbol-label bg-white bg-opacity-50">
                        <!--begin::Svg Icon | path: icons/duotune/abstract/abs043.svg-->
                        <span class="svg-icon svg-icon-1 svg-icon-dark">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="24"
                            height="24"
                            viewBox="0 0 24 24"
                            fill="none"
                          >
                            <path
                              opacity="0.3"
                              d="M22 8H8L12 4H19C19.6 4 20.2 4.39999 20.5 4.89999L22 8ZM3.5 19.1C3.8 19.7 4.4 20 5 20H12L16 16H2L3.5 19.1ZM19.1 20.5C19.7 20.2 20 19.6 20 19V12L16 8V22L19.1 20.5ZM4.9 3.5C4.3 3.8 4 4.4 4 5V12L8 16V2L4.9 3.5Z"
                              fill="currentColor"
                            ></path>
                            <path
                              d="M22 8L20 12L16 8H22ZM8 16L4 12L2 16H8ZM16 16L12 20L16 22V16ZM8 8L12 4L8 2V8Z"
                              fill="currentColor"
                            ></path>
                          </svg>
                        </span>
                        <!--end::Svg Icon-->
                      </div>
                    </div>
                    <!--end::Symbol-->
                    <!--begin::Title-->
                    <div>
                      <div class="fs-5 text-dark fw-bolder lh-1">23 หลัง</div>
                      <div class="fs-7 text-gray-600 fw-bold">
                        บ้านตึก2 ชั้น
                      </div>
                    </div>
                    <!--end::Title-->
                  </div>
                </div>
                <!--end::Col-->
                <!--begin::Col-->
                <div class="col-6">
                  <div class="d-flex align-items-center mb-9 me-2">
                    <!--begin::Symbol-->
                    <div class="symbol symbol-40px me-3">
                      <div class="symbol-label bg-white bg-opacity-50">
                        <!--begin::Svg Icon | path: icons/duotune/abstract/abs043.svg-->
                        <span class="svg-icon svg-icon-1 svg-icon-dark">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="24"
                            height="24"
                            viewBox="0 0 24 24"
                            fill="none"
                          >
                            <path
                              opacity="0.3"
                              d="M22 8H8L12 4H19C19.6 4 20.2 4.39999 20.5 4.89999L22 8ZM3.5 19.1C3.8 19.7 4.4 20 5 20H12L16 16H2L3.5 19.1ZM19.1 20.5C19.7 20.2 20 19.6 20 19V12L16 8V22L19.1 20.5ZM4.9 3.5C4.3 3.8 4 4.4 4 5V12L8 16V2L4.9 3.5Z"
                              fill="currentColor"
                            ></path>
                            <path
                              d="M22 8L20 12L16 8H22ZM8 16L4 12L2 16H8ZM16 16L12 20L16 22V16ZM8 8L12 4L8 2V8Z"
                              fill="currentColor"
                            ></path>
                          </svg>
                        </span>
                        <!--end::Svg Icon-->
                      </div>
                    </div>
                    <!--end::Symbol-->
                    <!--begin::Title-->
                    <div>
                      <div class="fs-5 text-dark fw-bolder lh-1">12 หลัง</div>
                      <div class="fs-7 text-gray-600 fw-bold">
                        บ้านไม้ชั้นเดียว
                      </div>
                    </div>
                    <!--end::Title-->
                  </div>
                </div>
                <!--end::Col-->
                <!--begin::Col-->
                <div class="col-6">
                  <div class="d-flex align-items-center mb-9 me-2">
                    <!--begin::Symbol-->
                    <div class="symbol symbol-40px me-3">
                      <div class="symbol-label bg-white bg-opacity-50">
                        <!--begin::Svg Icon | path: icons/duotune/abstract/abs043.svg-->
                        <span class="svg-icon svg-icon-1 svg-icon-dark">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="24"
                            height="24"
                            viewBox="0 0 24 24"
                            fill="none"
                          >
                            <path
                              opacity="0.3"
                              d="M22 8H8L12 4H19C19.6 4 20.2 4.39999 20.5 4.89999L22 8ZM3.5 19.1C3.8 19.7 4.4 20 5 20H12L16 16H2L3.5 19.1ZM19.1 20.5C19.7 20.2 20 19.6 20 19V12L16 8V22L19.1 20.5ZM4.9 3.5C4.3 3.8 4 4.4 4 5V12L8 16V2L4.9 3.5Z"
                              fill="currentColor"
                            ></path>
                            <path
                              d="M22 8L20 12L16 8H22ZM8 16L4 12L2 16H8ZM16 16L12 20L16 22V16ZM8 8L12 4L8 2V8Z"
                              fill="currentColor"
                            ></path>
                          </svg>
                        </span>
                        <!--end::Svg Icon-->
                      </div>
                    </div>
                    <!--end::Symbol-->
                    <!--begin::Title-->
                    <div>
                      <div class="fs-5 text-dark fw-bolder lh-1">32 หลัง</div>
                      <div class="fs-7 text-gray-600 fw-bold">
                        บ้านไม้ 2 ชั้น
                      </div>
                    </div>
                    <!--end::Title-->
                  </div>
                </div>
                <!--end::Col-->
                <!--begin::Col-->
                <div class="col-6">
                  <div class="d-flex align-items-center mb-9 me-2">
                    <!--begin::Symbol-->
                    <div class="symbol symbol-40px me-3">
                      <div class="symbol-label bg-white bg-opacity-50">
                        <!--begin::Svg Icon | path: icons/duotune/abstract/abs043.svg-->
                        <span class="svg-icon svg-icon-1 svg-icon-dark">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="24"
                            height="24"
                            viewBox="0 0 24 24"
                            fill="none"
                          >
                            <path
                              opacity="0.3"
                              d="M22 8H8L12 4H19C19.6 4 20.2 4.39999 20.5 4.89999L22 8ZM3.5 19.1C3.8 19.7 4.4 20 5 20H12L16 16H2L3.5 19.1ZM19.1 20.5C19.7 20.2 20 19.6 20 19V12L16 8V22L19.1 20.5ZM4.9 3.5C4.3 3.8 4 4.4 4 5V12L8 16V2L4.9 3.5Z"
                              fill="currentColor"
                            ></path>
                            <path
                              d="M22 8L20 12L16 8H22ZM8 16L4 12L2 16H8ZM16 16L12 20L16 22V16ZM8 8L12 4L8 2V8Z"
                              fill="currentColor"
                            ></path>
                          </svg>
                        </span>
                        <!--end::Svg Icon-->
                      </div>
                    </div>
                    <!--end::Symbol-->
                    <!--begin::Title-->
                    <div>
                      <div class="fs-5 text-dark fw-bolder lh-1">16 หลัง</div>
                      <div class="fs-7 text-gray-600 fw-bold">
                        บ้านครึ่งตึกครึ่งไม้ 2 ชั้น
                      </div>
                    </div>
                    <!--end::Title-->
                  </div>
                </div>
                <!--end::Col-->
                <!--begin::Col-->
                <div class="col-6">
                  <div class="d-flex align-items-center mb-9 ms-2">
                    <!--begin::Symbol-->
                    <div class="symbol symbol-40px me-3">
                      <div class="symbol-label bg-white bg-opacity-50">
                        <!--begin::Svg Icon | path: icons/duotune/abstract/abs046.svg-->
                        <span class="svg-icon svg-icon-1 svg-icon-dark">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="24"
                            height="24"
                            viewBox="0 0 24 24"
                            fill="none"
                          >
                            <path
                              d="M8 22C7.4 22 7 21.6 7 21V9C7 8.4 7.4 8 8 8C8.6 8 9 8.4 9 9V21C9 21.6 8.6 22 8 22Z"
                              fill="currentColor"
                            ></path>
                            <path
                              opacity="0.3"
                              d="M4 15C3.4 15 3 14.6 3 14V6C3 5.4 3.4 5 4 5C4.6 5 5 5.4 5 6V14C5 14.6 4.6 15 4 15ZM13 19V3C13 2.4 12.6 2 12 2C11.4 2 11 2.4 11 3V19C11 19.6 11.4 20 12 20C12.6 20 13 19.6 13 19ZM17 16V5C17 4.4 16.6 4 16 4C15.4 4 15 4.4 15 5V16C15 16.6 15.4 17 16 17C16.6 17 17 16.6 17 16ZM21 18V10C21 9.4 20.6 9 20 9C19.4 9 19 9.4 19 10V18C19 18.6 19.4 19 20 19C20.6 19 21 18.6 21 18Z"
                              fill="currentColor"
                            ></path>
                          </svg>
                        </span>
                        <!--end::Svg Icon-->
                      </div>
                    </div>
                    <!--end::Symbol-->
                    <!--begin::Title-->
                    <div>
                      <div class="fs-5 text-dark fw-bolder lh-1">15 หลัง</div>
                      <div class="fs-7 text-gray-600 fw-bold">
                        บ้านไม้ชั้นเดียวใต้ถุนสูง
                      </div>
                    </div>
                    <!--end::Title-->
                  </div>
                </div>
                <!--end::Col-->
                <!--begin::Col-->
                <div class="col-6">
                  <div class="d-flex align-items-center me-2">
                    <!--begin::Symbol-->
                    <div class="symbol symbol-40px me-3">
                      <div class="symbol-label bg-white bg-opacity-50">
                        <!--begin::Svg Icon | path: icons/duotune/abstract/abs022.svg-->
                        <span class="svg-icon svg-icon-1 svg-icon-dark">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="24"
                            height="24"
                            viewBox="0 0 24 24"
                            fill="none"
                          >
                            <path
                              opacity="0.3"
                              d="M11.425 7.325C12.925 5.825 15.225 5.825 16.725 7.325C18.225 8.825 18.225 11.125 16.725 12.625C15.225 14.125 12.925 14.125 11.425 12.625C9.92501 11.225 9.92501 8.825 11.425 7.325ZM8.42501 4.325C5.32501 7.425 5.32501 12.525 8.42501 15.625C11.525 18.725 16.625 18.725 19.725 15.625C22.825 12.525 22.825 7.425 19.725 4.325C16.525 1.225 11.525 1.225 8.42501 4.325Z"
                              fill="currentColor"
                            ></path>
                            <path
                              d="M11.325 17.525C10.025 18.025 8.425 17.725 7.325 16.725C5.825 15.225 5.825 12.925 7.325 11.425C8.825 9.92498 11.125 9.92498 12.625 11.425C13.225 12.025 13.625 12.925 13.725 13.725C14.825 13.825 15.925 13.525 16.725 12.625C17.125 12.225 17.425 11.825 17.525 11.325C17.125 10.225 16.525 9.22498 15.625 8.42498C12.525 5.32498 7.425 5.32498 4.325 8.42498C1.225 11.525 1.225 16.625 4.325 19.725C7.425 22.825 12.525 22.825 15.625 19.725C16.325 19.025 16.925 18.225 17.225 17.325C15.425 18.125 13.225 18.225 11.325 17.525Z"
                              fill="currentColor"
                            ></path>
                          </svg>
                        </span>
                        <!--end::Svg Icon-->
                      </div>
                    </div>
                    <!--end::Symbol-->
                    <!--begin::Title-->
                    <div>
                      <div class="fs-5 text-dark fw-bolder lh-1">6 หลัง</div>
                      <div class="fs-7 text-gray-600 fw-bold">อาคาร</div>
                    </div>
                    <!--end::Title-->
                  </div>
                </div>
                <!--end::Col-->
                <!--begin::Col-->
                <div class="col-6">
                  <div class="d-flex align-items-center ms-2">
                    <!--begin::Symbol-->
                    <div class="symbol symbol-40px me-3">
                      <div class="symbol-label bg-white bg-opacity-50">
                        <!--begin::Svg Icon | path: icons/duotune/abstract/abs045.svg-->
                        <span class="svg-icon svg-icon-1 svg-icon-dark">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="24"
                            height="24"
                            viewBox="0 0 24 24"
                            fill="none"
                          >
                            <path
                              d="M2 11.7127L10 14.1127L22 11.7127L14 9.31274L2 11.7127Z"
                              fill="currentColor"
                            ></path>
                            <path
                              opacity="0.3"
                              d="M20.9 7.91274L2 11.7127V6.81275C2 6.11275 2.50001 5.61274 3.10001 5.51274L20.6 2.01274C21.3 1.91274 22 2.41273 22 3.11273V6.61273C22 7.21273 21.5 7.81274 20.9 7.91274ZM22 16.6127V11.7127L3.10001 15.5127C2.50001 15.6127 2 16.2127 2 16.8127V20.3127C2 21.0127 2.69999 21.6128 3.39999 21.4128L20.9 17.9128C21.5 17.8128 22 17.2127 22 16.6127Z"
                              fill="currentColor"
                            ></path>
                          </svg>
                        </span>
                        <!--end::Svg Icon-->
                      </div>
                    </div>
                    <!--end::Symbol-->
                    <!--begin::Title-->
                    <div>
                      <div class="fs-5 text-dark fw-bolder lh-1">44 หลัง</div>
                      <div class="fs-7 text-gray-600 fw-bold">อื่นๆ</div>
                    </div>
                    <!--end::Title-->
                  </div>
                </div>
                <!--end::Col-->
              </div>
              <!--end::Row-->
            </div>
          </div>

          <div
            class="card card-xxl-stretch mb-5 mb-xl-8"
            style="background-color: #cbd4f4"
          >
            <!--begin::Body-->
            <div class="card-body d-flex flex-column">
              <!--begin::Wrapper-->
              <div class="d-flex flex-column mb-7">
                <!--begin::Title-->
                <a href="#" class="text-dark text-hover-primary fw-bolder fs-3"
                  >อัตราเช่า</a
                >
                <!--end::Title-->
              </div>
              <!--end::Wrapper-->
              <!--begin::Row-->
              <div class="row g-0">
                <!--begin::Col-->
                <div class="col-6">
                  <div class="d-flex align-items-center mb-9 me-2">
                    <!--begin::Symbol-->
                    <div class="symbol symbol-40px me-3">
                      <div class="symbol-label bg-white bg-opacity-50">
                        <!--begin::Svg Icon | path: icons/duotune/abstract/abs043.svg-->
                        <span class="svg-icon svg-icon-1 svg-icon-dark">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="24"
                            height="24"
                            viewBox="0 0 24 24"
                            fill="none"
                          >
                            <path
                              opacity="0.3"
                              d="M22 8H8L12 4H19C19.6 4 20.2 4.39999 20.5 4.89999L22 8ZM3.5 19.1C3.8 19.7 4.4 20 5 20H12L16 16H2L3.5 19.1ZM19.1 20.5C19.7 20.2 20 19.6 20 19V12L16 8V22L19.1 20.5ZM4.9 3.5C4.3 3.8 4 4.4 4 5V12L8 16V2L4.9 3.5Z"
                              fill="currentColor"
                            ></path>
                            <path
                              d="M22 8L20 12L16 8H22ZM8 16L4 12L2 16H8ZM16 16L12 20L16 22V16ZM8 8L12 4L8 2V8Z"
                              fill="currentColor"
                            ></path>
                          </svg>
                        </span>
                        <!--end::Svg Icon-->
                      </div>
                    </div>
                    <!--end::Symbol-->
                    <!--begin::Title-->
                    <div>
                      <div class="fs-5 text-dark fw-bolder lh-1">120 อัตรา</div>
                      <div class="fs-7 text-gray-600 fw-bold">อัตราที่ดิน</div>
                    </div>
                    <!--end::Title-->
                  </div>
                </div>
                <!--end::Col-->
                <!--begin::Col-->
                <div class="col-6">
                  <div class="d-flex align-items-center mb-9 ms-2">
                    <!--begin::Symbol-->
                    <div class="symbol symbol-40px me-3">
                      <div class="symbol-label bg-white bg-opacity-50">
                        <!--begin::Svg Icon | path: icons/duotune/abstract/abs046.svg-->
                        <span class="svg-icon svg-icon-1 svg-icon-dark">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="24"
                            height="24"
                            viewBox="0 0 24 24"
                            fill="none"
                          >
                            <path
                              d="M8 22C7.4 22 7 21.6 7 21V9C7 8.4 7.4 8 8 8C8.6 8 9 8.4 9 9V21C9 21.6 8.6 22 8 22Z"
                              fill="currentColor"
                            ></path>
                            <path
                              opacity="0.3"
                              d="M4 15C3.4 15 3 14.6 3 14V6C3 5.4 3.4 5 4 5C4.6 5 5 5.4 5 6V14C5 14.6 4.6 15 4 15ZM13 19V3C13 2.4 12.6 2 12 2C11.4 2 11 2.4 11 3V19C11 19.6 11.4 20 12 20C12.6 20 13 19.6 13 19ZM17 16V5C17 4.4 16.6 4 16 4C15.4 4 15 4.4 15 5V16C15 16.6 15.4 17 16 17C16.6 17 17 16.6 17 16ZM21 18V10C21 9.4 20.6 9 20 9C19.4 9 19 9.4 19 10V18C19 18.6 19.4 19 20 19C20.6 19 21 18.6 21 18Z"
                              fill="currentColor"
                            ></path>
                          </svg>
                        </span>
                        <!--end::Svg Icon-->
                      </div>
                    </div>
                    <!--end::Symbol-->
                    <!--begin::Title-->
                    <div>
                      <div class="fs-5 text-dark fw-bolder lh-1">32 อัตรา</div>
                      <div class="fs-7 text-gray-600 fw-bold">อัตราอาคาร</div>
                    </div>
                    <!--end::Title-->
                  </div>
                </div>
                <!--end::Col-->
              </div>
              <!--end::Row-->
            </div>
          </div>
          <!--end::License-->

          <div class="card card-xl-stretch mb-xl-8">
            <!--begin::Body-->
            <div class="card-body p-0">
              <!--begin::Header-->
              <div class="px-9 pt-7 card-rounded h-275px w-100 bg-primary">
                <!--begin::Heading-->
                <div class="d-flex flex-stack">
                  <h3 class="m-0 text-white fw-bolder fs-3">
                    ราคาประเมินที่ดิน
                  </h3>
                </div>
                <!--end::Heading-->
                <!--begin::Balance-->
                <div class="d-flex text-center flex-column text-white pt-8">
                  <span class="fw-bold fs-7">ปัจจุบัน ปี 2566</span>
                  <span class="fw-bolder fs-2x pt-1">230,000 บาท/ตารางวา</span>
                </div>
                <!--end::Balance-->
              </div>
              <!--end::Header-->
              <!--begin::Items-->
              <div
                class="bg-body shadow-sm card-rounded mx-9 mb-9 px-6 py-9 position-relative z-index-1"
                style="margin-top: -100px"
              >
                <!--begin::Item-->
                <div class="d-flex align-items-center mb-6">
                  <!--begin::Symbol-->
                  <div class="symbol symbol-45px w-40px me-5">
                    <span class="symbol-label bg-lighten">
                      <!--begin::Svg Icon | path: icons/duotune/maps/map004.svg-->
                      <span class="svg-icon svg-icon-1">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="24"
                          height="24"
                          viewBox="0 0 24 24"
                          fill="none"
                        >
                          <path
                            opacity="0.3"
                            d="M18.4 5.59998C21.9 9.09998 21.9 14.8 18.4 18.3C14.9 21.8 9.2 21.8 5.7 18.3L18.4 5.59998Z"
                            fill="currentColor"
                          ></path>
                          <path
                            d="M12 2C6.5 2 2 6.5 2 12C2 17.5 6.5 22 12 22C17.5 22 22 17.5 22 12C22 6.5 17.5 2 12 2ZM19.9 11H13V8.8999C14.9 8.6999 16.7 8.00005 18.1 6.80005C19.1 8.00005 19.7 9.4 19.9 11ZM11 19.8999C9.7 19.6999 8.39999 19.2 7.39999 18.5C8.49999 17.7 9.7 17.2001 11 17.1001V19.8999ZM5.89999 6.90002C7.39999 8.10002 9.2 8.8 11 9V11.1001H4.10001C4.30001 9.4001 4.89999 8.00002 5.89999 6.90002ZM7.39999 5.5C8.49999 4.7 9.7 4.19998 11 4.09998V7C9.7 6.8 8.39999 6.3 7.39999 5.5ZM13 17.1001C14.3 17.3001 15.6 17.8 16.6 18.5C15.5 19.3 14.3 19.7999 13 19.8999V17.1001ZM13 4.09998C14.3 4.29998 15.6 4.8 16.6 5.5C15.5 6.3 14.3 6.80002 13 6.90002V4.09998ZM4.10001 13H11V15.1001C9.1 15.3001 7.29999 16 5.89999 17.2C4.89999 16 4.30001 14.6 4.10001 13ZM18.1 17.1001C16.6 15.9001 14.8 15.2 13 15V12.8999H19.9C19.7 14.5999 19.1 16.0001 18.1 17.1001Z"
                            fill="currentColor"
                          ></path>
                        </svg>
                      </span>
                      <!--end::Svg Icon-->
                    </span>
                  </div>
                  <!--end::Symbol-->
                  <!--begin::Description-->
                  <div class="d-flex align-items-center flex-wrap w-100">
                    <!--begin::Title-->
                    <div class="mb-1 pe-3 flex-grow-1">
                      <a
                        href="#"
                        class="fs-5 text-gray-800 text-hover-primary fw-bolder"
                        >210,000 บาท/ตารางวา</a
                      >
                      <div class="text-gray-400 fw-bold fs-7">ปี 2563</div>
                    </div>
                    <!--end::Title-->
                    <!--begin::Label-->
                    <div class="d-flex align-items-center">
                      <div class="fw-bolder fs-5 text-gray-800 pe-1">
                        10,000
                      </div>
                      <!--begin::Svg Icon | path: icons/duotune/arrows/arr066.svg-->
                      <span class="svg-icon svg-icon-5 svg-icon-success ms-1">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="24"
                          height="24"
                          viewBox="0 0 24 24"
                          fill="none"
                        >
                          <rect
                            opacity="0.5"
                            x="13"
                            y="6"
                            width="13"
                            height="2"
                            rx="1"
                            transform="rotate(90 13 6)"
                            fill="currentColor"
                          ></rect>
                          <path
                            d="M12.5657 8.56569L16.75 12.75C17.1642 13.1642 17.8358 13.1642 18.25 12.75C18.6642 12.3358 18.6642 11.6642 18.25 11.25L12.7071 5.70711C12.3166 5.31658 11.6834 5.31658 11.2929 5.70711L5.75 11.25C5.33579 11.6642 5.33579 12.3358 5.75 12.75C6.16421 13.1642 6.83579 13.1642 7.25 12.75L11.4343 8.56569C11.7467 8.25327 12.2533 8.25327 12.5657 8.56569Z"
                            fill="currentColor"
                          ></path>
                        </svg>
                      </span>
                      <!--end::Svg Icon-->
                    </div>
                    <!--end::Label-->
                  </div>
                  <!--end::Description-->
                </div>
                <!--end::Item-->
                <!--begin::Item-->
                <div class="d-flex align-items-center mb-6">
                  <!--begin::Symbol-->
                  <div class="symbol symbol-45px w-40px me-5">
                    <span class="symbol-label bg-lighten">
                      <!--begin::Svg Icon | path: icons/duotune/general/gen025.svg-->
                      <span class="svg-icon svg-icon-1">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="24"
                          height="24"
                          viewBox="0 0 24 24"
                          fill="none"
                        >
                          <rect
                            x="2"
                            y="2"
                            width="9"
                            height="9"
                            rx="2"
                            fill="currentColor"
                          ></rect>
                          <rect
                            opacity="0.3"
                            x="13"
                            y="2"
                            width="9"
                            height="9"
                            rx="2"
                            fill="currentColor"
                          ></rect>
                          <rect
                            opacity="0.3"
                            x="13"
                            y="13"
                            width="9"
                            height="9"
                            rx="2"
                            fill="currentColor"
                          ></rect>
                          <rect
                            opacity="0.3"
                            x="2"
                            y="13"
                            width="9"
                            height="9"
                            rx="2"
                            fill="currentColor"
                          ></rect>
                        </svg>
                      </span>
                      <!--end::Svg Icon-->
                    </span>
                  </div>
                  <!--end::Symbol-->
                  <!--begin::Description-->
                  <div class="d-flex align-items-center flex-wrap w-100">
                    <!--begin::Title-->
                    <div class="mb-1 pe-3 flex-grow-1">
                      <a
                        href="#"
                        class="fs-5 text-gray-800 text-hover-primary fw-bolder"
                        >200,000 บาท/ตารางวา</a
                      >
                      <div class="text-gray-400 fw-bold fs-7">ปี 2560</div>
                    </div>
                    <!--end::Title-->
                    <!--begin::Label-->
                    <div class="d-flex align-items-center">
                      <div class="fw-bolder fs-5 text-gray-800 pe-1">-</div>
                      <!--begin::Svg Icon | path: icons/duotune/arrows/arr065.svg-->
                      <span class="svg-icon svg-icon-5 svg-icon-danger ms-1">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="24"
                          height="24"
                          viewBox="0 0 24 24"
                          fill="none"
                        >
                          <rect
                            opacity="0.5"
                            x="11"
                            y="18"
                            width="13"
                            height="2"
                            rx="1"
                            transform="rotate(-90 11 18)"
                            fill="currentColor"
                          ></rect>
                          <path
                            d="M11.4343 15.4343L7.25 11.25C6.83579 10.8358 6.16421 10.8358 5.75 11.25C5.33579 11.6642 5.33579 12.3358 5.75 12.75L11.2929 18.2929C11.6834 18.6834 12.3166 18.6834 12.7071 18.2929L18.25 12.75C18.6642 12.3358 18.6642 11.6642 18.25 11.25C17.8358 10.8358 17.1642 10.8358 16.75 11.25L12.5657 15.4343C12.2533 15.7467 11.7467 15.7467 11.4343 15.4343Z"
                            fill="currentColor"
                          ></path>
                        </svg>
                      </span>
                      <!--end::Svg Icon-->
                    </div>
                    <!--end::Label-->
                  </div>
                  <!--end::Description-->
                </div>
                <!--end::Item-->
              </div>
              <!--end::Items-->
            </div>
            <!--end::Body-->
          </div>
        </div>
        <!--end::Wrapper-->
      </div>
      <!--end::Content-->
    </div>
    <!--end::Body-->
  </div>
  <!--end::Card-->
</div>
<!--end::description_รายละเอียดโฉนดและสรุปข้อมูล drawer-->

<!--begin::Help drawer-->
{% comment %}
<div
  id="kt_help"
  class="bg-body"
  data-kt-drawer="true"
  data-kt-drawer-name="help"
  data-kt-drawer-activate="true"
  data-kt-drawer-overlay="true"
  data-kt-drawer-width="{default:'350px', 'md': '525px'}"
  data-kt-drawer-direction="end"
  data-kt-drawer-toggle="#kt_help_toggle"
  data-kt-drawer-close="#kt_help_close"
>
  <!--begin::Card-->
  <div class="card shadow-none rounded-0 w-100">
    <!--begin::Header-->
    <div class="card-header" id="kt_help_header">
      <h5 class="card-title fw-semibold text-gray-600">Learn & Get Inspired</h5>
      <div class="card-toolbar">
        <button
          type="button"
          class="btn btn-sm btn-icon explore-btn-dismiss me-n5"
          id="kt_help_close"
        >
          <!--begin::Svg Icon | path: icons/duotune/arrows/arr061.svg-->
          <span class="svg-icon svg-icon-2">
            <svg
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <rect
                opacity="0.5"
                x="6"
                y="17.3137"
                width="16"
                height="2"
                rx="1"
                transform="rotate(-45 6 17.3137)"
                fill="currentColor"
              />
              <rect
                x="7.41422"
                y="6"
                width="16"
                height="2"
                rx="1"
                transform="rotate(45 7.41422 6)"
                fill="currentColor"
              />
            </svg>
          </span>
          <!--end::Svg Icon-->
        </button>
      </div>
    </div>
    <!--end::Header-->
    <!--begin::Body-->
    <div class="card-body" id="kt_help_body">
      <!--begin::Content-->
      <div
        id="kt_help_scroll"
        class="hover-scroll-overlay-y"
        data-kt-scroll="true"
        data-kt-scroll-height="auto"
        data-kt-scroll-wrappers="#kt_help_body"
        data-kt-scroll-dependencies="#kt_help_header"
        data-kt-scroll-offset="5px"
      >
        <!--begin::Support-->
        <div
          class="rounded border border-dashed border-gray-300 p-6 p-lg-8 mb-10"
        >
          <!--begin::Heading-->
          <h2 class="fw-bold mb-5">
            Support at
            <a href="https://devs.keenthemes.com" class=""
              >devs.keenthemes.com</a
            >
          </h2>
          <!--end::Heading-->
          <!--begin::Description-->
          <div class="fs-5 fw-semibold mb-5">
            <span class="text-gray-500"
              >Join our developers community to find answer to your question and
              help others.</span
            >
            <a
              class="explore-link d-none"
              href="https://keenthemes.com/licensing"
              >FAQs</a
            >
          </div>
          <!--end::Description-->
          <!--begin::Link-->
          <a
            href="https://devs.keenthemes.com"
            class="btn btn-lg explore-btn-primary w-100"
            >Get Support</a
          >
          <!--end::Link-->
        </div>
        <!--end::Support-->
        <!--begin::Link-->
        <div class="d-flex align-items-center mb-7">
          <!--begin::Icon-->
          <div
            class="d-flex flex-center w-50px h-50px w-lg-75px h-lg-75px flex-shrink-0 rounded bg-light-warning"
          >
            <!--begin::Svg Icon | path: icons/duotune/abstract/abs027.svg-->
            <span class="svg-icon svg-icon-warning svg-icon-2x svg-icon-lg-3x">
              <svg
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  opacity="0.3"
                  d="M21.25 18.525L13.05 21.825C12.35 22.125 11.65 22.125 10.95 21.825L2.75 18.525C1.75 18.125 1.75 16.725 2.75 16.325L4.04999 15.825L10.25 18.325C10.85 18.525 11.45 18.625 12.05 18.625C12.65 18.625 13.25 18.525 13.85 18.325L20.05 15.825L21.35 16.325C22.35 16.725 22.35 18.125 21.25 18.525ZM13.05 16.425L21.25 13.125C22.25 12.725 22.25 11.325 21.25 10.925L13.05 7.62502C12.35 7.32502 11.65 7.32502 10.95 7.62502L2.75 10.925C1.75 11.325 1.75 12.725 2.75 13.125L10.95 16.425C11.65 16.725 12.45 16.725 13.05 16.425Z"
                  fill="currentColor"
                />
                <path
                  d="M11.05 11.025L2.84998 7.725C1.84998 7.325 1.84998 5.925 2.84998 5.525L11.05 2.225C11.75 1.925 12.45 1.925 13.15 2.225L21.35 5.525C22.35 5.925 22.35 7.325 21.35 7.725L13.05 11.025C12.45 11.325 11.65 11.325 11.05 11.025Z"
                  fill="currentColor"
                />
              </svg>
            </span>
            <!--end::Svg Icon-->
          </div>
          <!--end::Icon-->
          <!--begin::Info-->
          <div class="d-flex flex-stack flex-grow-1 ms-4 ms-lg-6">
            <!--begin::Wrapper-->
            <div class="d-flex flex-column me-2 me-lg-5">
              <!--begin::Title-->
              <a
                href="https://preview.keenthemes.com/html/metronic/docs"
                class="text-dark text-hover-primary fw-bold fs-6 fs-lg-4 mb-1"
                >Documentation & Videos</a
              >
              <!--end::Title-->
              <!--begin::Description-->
              <div class="text-muted fw-semibold fs-7 fs-lg-6">
                From guides and video tutorials, to live demos and code examples
                to get started.
              </div>
              <!--end::Description-->
            </div>
            <!--end::Wrapper-->
            <!--begin::Svg Icon | path: icons/duotune/arrows/arr064.svg-->
            <span class="svg-icon svg-icon-gray-400 svg-icon-2">
              <svg
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <rect
                  opacity="0.5"
                  x="18"
                  y="13"
                  width="13"
                  height="2"
                  rx="1"
                  transform="rotate(-180 18 13)"
                  fill="currentColor"
                />
                <path
                  d="M15.4343 12.5657L11.25 16.75C10.8358 17.1642 10.8358 17.8358 11.25 18.25C11.6642 18.6642 12.3358 18.6642 12.75 18.25L18.2929 12.7071C18.6834 12.3166 18.6834 11.6834 18.2929 11.2929L12.75 5.75C12.3358 5.33579 11.6642 5.33579 11.25 5.75C10.8358 6.16421 10.8358 6.83579 11.25 7.25L15.4343 11.4343C15.7467 11.7467 15.7467 12.2533 15.4343 12.5657Z"
                  fill="currentColor"
                />
              </svg>
            </span>
            <!--end::Svg Icon-->
          </div>
          <!--end::Info-->
        </div>
        <!--end::Link-->
        <!--begin::Link-->
        <div class="d-flex align-items-center mb-7">
          <!--begin::Icon-->
          <div
            class="d-flex flex-center w-50px h-50px w-lg-75px h-lg-75px flex-shrink-0 rounded bg-light-primary"
          >
            <!--begin::Svg Icon | path: icons/duotune/ecommerce/ecm007.svg-->
            <span class="svg-icon svg-icon-primary svg-icon-2x svg-icon-lg-3x">
              <svg
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M21 9V11C21 11.6 20.6 12 20 12H14V8H20C20.6 8 21 8.4 21 9ZM10 8H4C3.4 8 3 8.4 3 9V11C3 11.6 3.4 12 4 12H10V8Z"
                  fill="currentColor"
                />
                <path
                  d="M15 2C13.3 2 12 3.3 12 5V8H15C16.7 8 18 6.7 18 5C18 3.3 16.7 2 15 2Z"
                  fill="currentColor"
                />
                <path
                  opacity="0.3"
                  d="M9 2C10.7 2 12 3.3 12 5V8H9C7.3 8 6 6.7 6 5C6 3.3 7.3 2 9 2ZM4 12V21C4 21.6 4.4 22 5 22H10V12H4ZM20 12V21C20 21.6 19.6 22 19 22H14V12H20Z"
                  fill="currentColor"
                />
              </svg>
            </span>
            <!--end::Svg Icon-->
          </div>
          <!--end::Icon-->
          <!--begin::Info-->
          <div class="d-flex flex-stack flex-grow-1 ms-4 ms-lg-6">
            <!--begin::Wrapper-->
            <div class="d-flex flex-column me-2 me-lg-5">
              <!--begin::Title-->
              <a
                href="https://preview.keenthemes.com/html/metronic/docs//base/utilities"
                class="text-dark text-hover-primary fw-bold fs-6 fs-lg-4 mb-1"
                >Plugins & Components</a
              >
              <!--end::Title-->
              <!--begin::Description-->
              <div class="text-muted fw-semibold fs-7 fs-lg-6">
                Check out our 300+ in-house components and customized 3rd-party
                plugins.
              </div>
              <!--end::Description-->
            </div>
            <!--end::Wrapper-->
            <!--begin::Svg Icon | path: icons/duotune/arrows/arr064.svg-->
            <span class="svg-icon svg-icon-gray-400 svg-icon-2">
              <svg
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <rect
                  opacity="0.5"
                  x="18"
                  y="13"
                  width="13"
                  height="2"
                  rx="1"
                  transform="rotate(-180 18 13)"
                  fill="currentColor"
                />
                <path
                  d="M15.4343 12.5657L11.25 16.75C10.8358 17.1642 10.8358 17.8358 11.25 18.25C11.6642 18.6642 12.3358 18.6642 12.75 18.25L18.2929 12.7071C18.6834 12.3166 18.6834 11.6834 18.2929 11.2929L12.75 5.75C12.3358 5.33579 11.6642 5.33579 11.25 5.75C10.8358 6.16421 10.8358 6.83579 11.25 7.25L15.4343 11.4343C15.7467 11.7467 15.7467 12.2533 15.4343 12.5657Z"
                  fill="currentColor"
                />
              </svg>
            </span>
            <!--end::Svg Icon-->
          </div>
          <!--end::Info-->
        </div>
        <!--end::Link-->
        <!--begin::Link-->
        <div class="d-flex align-items-center mb-7">
          <!--begin::Icon-->
          <div
            class="d-flex flex-center w-50px h-50px w-lg-75px h-lg-75px flex-shrink-0 rounded bg-light-info"
          >
            <!--begin::Svg Icon | path: icons/duotune/art/art006.svg-->
            <span class="svg-icon svg-icon-info svg-icon-2x svg-icon-lg-3x">
              <svg
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  opacity="0.3"
                  d="M22 19V17C22 16.4 21.6 16 21 16H8V3C8 2.4 7.6 2 7 2H5C4.4 2 4 2.4 4 3V19C4 19.6 4.4 20 5 20H21C21.6 20 22 19.6 22 19Z"
                  fill="currentColor"
                />
                <path
                  d="M20 5V21C20 21.6 19.6 22 19 22H17C16.4 22 16 21.6 16 21V8H8V4H19C19.6 4 20 4.4 20 5ZM3 8H4V4H3C2.4 4 2 4.4 2 5V7C2 7.6 2.4 8 3 8Z"
                  fill="currentColor"
                />
              </svg>
            </span>
            <!--end::Svg Icon-->
          </div>
          <!--end::Icon-->
          <!--begin::Info-->
          <div class="d-flex flex-stack flex-grow-1 ms-4 ms-lg-6">
            <!--begin::Wrapper-->
            <div class="d-flex flex-column me-2 me-lg-5">
              <!--begin::Title-->
              <a
                href="https://preview.keenthemes.com/metronic8/layout-builder.html"
                class="text-dark text-hover-primary fw-bold fs-6 fs-lg-4 mb-1"
                >Layout Builder</a
              >
              <!--end::Title-->
              <!--begin::Description-->
              <div class="text-muted fw-semibold fs-7 fs-lg-6">
                Build your layout, preview it and export the HTML for server
                side integration.
              </div>
              <!--end::Description-->
            </div>
            <!--end::Wrapper-->
            <!--begin::Svg Icon | path: icons/duotune/arrows/arr064.svg-->
            <span class="svg-icon svg-icon-gray-400 svg-icon-2">
              <svg
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <rect
                  opacity="0.5"
                  x="18"
                  y="13"
                  width="13"
                  height="2"
                  rx="1"
                  transform="rotate(-180 18 13)"
                  fill="currentColor"
                />
                <path
                  d="M15.4343 12.5657L11.25 16.75C10.8358 17.1642 10.8358 17.8358 11.25 18.25C11.6642 18.6642 12.3358 18.6642 12.75 18.25L18.2929 12.7071C18.6834 12.3166 18.6834 11.6834 18.2929 11.2929L12.75 5.75C12.3358 5.33579 11.6642 5.33579 11.25 5.75C10.8358 6.16421 10.8358 6.83579 11.25 7.25L15.4343 11.4343C15.7467 11.7467 15.7467 12.2533 15.4343 12.5657Z"
                  fill="currentColor"
                />
              </svg>
            </span>
            <!--end::Svg Icon-->
          </div>
          <!--end::Info-->
        </div>
        <!--end::Link-->
        <!--begin::Link-->
        <div class="d-flex align-items-center mb-7">
          <!--begin::Icon-->
          <div
            class="d-flex flex-center w-50px h-50px w-lg-75px h-lg-75px flex-shrink-0 rounded bg-light-success"
          >
            <!--begin::Svg Icon | path: icons/duotune/files/fil021.svg-->
            <span class="svg-icon svg-icon-success svg-icon-2x svg-icon-lg-3x">
              <svg
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  opacity="0.3"
                  d="M19 15C20.7 15 22 13.7 22 12C22 10.3 20.7 9 19 9C18.9 9 18.9 9 18.8 9C18.9 8.7 19 8.3 19 8C19 6.3 17.7 5 16 5C15.4 5 14.8 5.2 14.3 5.5C13.4 4 11.8 3 10 3C7.2 3 5 5.2 5 8C5 8.3 5 8.7 5.1 9H5C3.3 9 2 10.3 2 12C2 13.7 3.3 15 5 15H19Z"
                  fill="currentColor"
                />
                <path
                  d="M13 17.4V12C13 11.4 12.6 11 12 11C11.4 11 11 11.4 11 12V17.4H13Z"
                  fill="currentColor"
                />
                <path
                  opacity="0.3"
                  d="M8 17.4H16L12.7 20.7C12.3 21.1 11.7 21.1 11.3 20.7L8 17.4Z"
                  fill="currentColor"
                />
              </svg>
            </span>
            <!--end::Svg Icon-->
          </div>
          <!--end::Icon-->
          <!--begin::Info-->
          <div class="d-flex flex-stack flex-grow-1 ms-4 ms-lg-6">
            <!--begin::Wrapper-->
            <div class="d-flex flex-column me-2 me-lg-5">
              <!--begin::Title-->
              <a
                href="https://devs.keenthemes.com/metronic"
                class="text-dark text-hover-primary fw-bold fs-6 fs-lg-4 mb-1"
                >Metronic Downloads</a
              >
              <!--end::Title-->
              <!--begin::Description-->
              <div class="text-muted fw-semibold fs-7 fs-lg-6">
                Download your prefered framework and demo with one click.
              </div>
              <!--end::Description-->
            </div>
            <!--end::Wrapper-->
            <!--begin::Svg Icon | path: icons/duotune/arrows/arr064.svg-->
            <span class="svg-icon svg-icon-gray-400 svg-icon-2">
              <svg
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <rect
                  opacity="0.5"
                  x="18"
                  y="13"
                  width="13"
                  height="2"
                  rx="1"
                  transform="rotate(-180 18 13)"
                  fill="currentColor"
                />
                <path
                  d="M15.4343 12.5657L11.25 16.75C10.8358 17.1642 10.8358 17.8358 11.25 18.25C11.6642 18.6642 12.3358 18.6642 12.75 18.25L18.2929 12.7071C18.6834 12.3166 18.6834 11.6834 18.2929 11.2929L12.75 5.75C12.3358 5.33579 11.6642 5.33579 11.25 5.75C10.8358 6.16421 10.8358 6.83579 11.25 7.25L15.4343 11.4343C15.7467 11.7467 15.7467 12.2533 15.4343 12.5657Z"
                  fill="currentColor"
                />
              </svg>
            </span>
            <!--end::Svg Icon-->
          </div>
          <!--end::Info-->
        </div>
        <!--end::Link-->
        <!--begin::Link-->
        <div class="d-flex align-items-center mb-7">
          <!--begin::Icon-->
          <div
            class="d-flex flex-center w-50px h-50px w-lg-75px h-lg-75px flex-shrink-0 rounded bg-light-danger"
          >
            <!--begin::Svg Icon | path: icons/duotune/electronics/elc009.svg-->
            <span class="svg-icon svg-icon-danger svg-icon-2x svg-icon-lg-3x">
              <svg
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M13 9V8C13 7.4 13.4 7 14 7H15C16.7 7 18 5.7 18 4V3C18 2.4 17.6 2 17 2C16.4 2 16 2.4 16 3V4C16 4.6 15.6 5 15 5H14C12.3 5 11 6.3 11 8V9H13Z"
                  fill="currentColor"
                />
                <path
                  opacity="0.3"
                  d="M21 22H3C2.4 22 2 21.6 2 21V10C2 9.4 2.4 9 3 9H21C21.6 9 22 9.4 22 10V21C22 21.6 21.6 22 21 22ZM5 12C4.4 12 4 12.4 4 13C4 13.6 4.4 14 5 14C5.6 14 6 13.6 6 13C6 12.4 5.6 12 5 12ZM8 12C7.4 12 7 12.4 7 13C7 13.6 7.4 14 8 14C8.6 14 9 13.6 9 13C9 12.4 8.6 12 8 12ZM11 12C10.4 12 10 12.4 10 13C10 13.6 10.4 14 11 14C11.6 14 12 13.6 12 13C12 12.4 11.6 12 11 12ZM14 12C13.4 12 13 12.4 13 13C13 13.6 13.4 14 14 14C14.6 14 15 13.6 15 13C15 12.4 14.6 12 14 12ZM9 15C8.4 15 8 15.4 8 16C8 16.6 8.4 17 9 17C9.6 17 10 16.6 10 16C10 15.4 9.6 15 9 15ZM12 15C11.4 15 11 15.4 11 16C11 16.6 11.4 17 12 17C12.6 17 13 16.6 13 16C13 15.4 12.6 15 12 15ZM15 15C14.4 15 14 15.4 14 16C14 16.6 14.4 17 15 17C15.6 17 16 16.6 16 16C16 15.4 15.6 15 15 15ZM19 18C18.4 18 18 18.4 18 19C18 19.6 18.4 20 19 20C19.6 20 20 19.6 20 19C20 18.4 19.6 18 19 18ZM7 19C7 18.4 6.6 18 6 18H5C4.4 18 4 18.4 4 19C4 19.6 4.4 20 5 20H6C6.6 20 7 19.6 7 19ZM7 16C7 15.4 6.6 15 6 15H5C4.4 15 4 15.4 4 16C4 16.6 4.4 17 5 17H6C6.6 17 7 16.6 7 16ZM17 14H19C19.6 14 20 13.6 20 13C20 12.4 19.6 12 19 12H17C16.4 12 16 12.4 16 13C16 13.6 16.4 14 17 14ZM18 17H19C19.6 17 20 16.6 20 16C20 15.4 19.6 15 19 15H18C17.4 15 17 15.4 17 16C17 16.6 17.4 17 18 17ZM17 19C17 18.4 16.6 18 16 18H9C8.4 18 8 18.4 8 19C8 19.6 8.4 20 9 20H16C16.6 20 17 19.6 17 19Z"
                  fill="currentColor"
                />
              </svg>
            </span>
            <!--end::Svg Icon-->
          </div>
          <!--end::Icon-->
          <!--begin::Info-->
          <div class="d-flex flex-stack flex-grow-1 ms-4 ms-lg-6">
            <!--begin::Wrapper-->
            <div class="d-flex flex-column me-2 me-lg-5">
              <!--begin::Title-->
              <a
                href="https://preview.keenthemes.com/html/metronic/docs/getting-started/changelog"
                class="text-dark text-hover-primary fw-bold fs-6 fs-lg-4 mb-1"
                >What's New ?</a
              >
              <!--end::Title-->
              <!--begin::Description-->
              <div class="text-muted fw-semibold fs-7 fs-lg-6">
                Latest features and improvements added with our users feedback
                in mind.
              </div>
              <!--end::Description-->
            </div>
            <!--end::Wrapper-->
            <!--begin::Svg Icon | path: icons/duotune/arrows/arr064.svg-->
            <span class="svg-icon svg-icon-gray-400 svg-icon-2">
              <svg
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <rect
                  opacity="0.5"
                  x="18"
                  y="13"
                  width="13"
                  height="2"
                  rx="1"
                  transform="rotate(-180 18 13)"
                  fill="currentColor"
                />
                <path
                  d="M15.4343 12.5657L11.25 16.75C10.8358 17.1642 10.8358 17.8358 11.25 18.25C11.6642 18.6642 12.3358 18.6642 12.75 18.25L18.2929 12.7071C18.6834 12.3166 18.6834 11.6834 18.2929 11.2929L12.75 5.75C12.3358 5.33579 11.6642 5.33579 11.25 5.75C10.8358 6.16421 10.8358 6.83579 11.25 7.25L15.4343 11.4343C15.7467 11.7467 15.7467 12.2533 15.4343 12.5657Z"
                  fill="currentColor"
                />
              </svg>
            </span>
            <!--end::Svg Icon-->
          </div>
          <!--end::Info-->
        </div>
        <!--end::Link-->
      </div>
      <!--end::Content-->
    </div>
    <!--end::Body-->
  </div>
  <!--end::Card-->
</div>
{% endcomment %}
<!--end::Help drawer-->
<!--end::Engage drawers-->

{% endblock content_map %} {% block js %}



<!-- leaflet_top js -->
<script>
  var lyr_point;
  var group_point;
  var lyr_deeds;
  var layer_shp_rents;
  // javascript get base url
  var base_url = window.location.origin;
  $(document).ready(function () {
    

    // BASE MAP
    const osm = L.tileLayer(
      "https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png",
      {
        maxNativeZoom: 200,
        maxZoom: 200,
        attribution:
          "Map data © <a href='https://openstreetmap.org'>OpenStreetMap</a> contributors",
      }
    );
    
    // const esri = L.tileLayer(
    //   "https://server.arcgisonline.com/ArcGIS/rest/services/World_Street_Map/MapServer/tile/{z}/{y}/{x}",
    //   {
    //     attribution:
    //       "Tiles &copy; Esri &mdash; Source: Esri, i-cubed, USDA, USGS, AEX, GeoEye, Getmapping, Aerogrid, IGN, IGP, UPR-EGP, and the GIS User Community",
    //   }
    // );
    // const ggl = L.tileLayer(
    //   "https://mt1.google.com/vt/lyrs=m&x={x}&y={y}&z={z}",
    //   {
    //     maxNativeZoom: 200,
    //       maxZoom: 200,
    //       attribution: '&copy; <a href="#">Developed By นายบัญชา ไวเปีย</a>',
    //   }
    // );
    // ggl2 = L.tileLayer(
    //   "https://mt1.google.com/vt/lyrs=s&x={x}&y={y}&z={z}",
    //   {
    //       maxNativeZoom: 200,
    //       maxZoom: 200,
    //       attribution: '&copy; <a href="#">Developed By นายบัญชา ไวเปีย</a>',
    //   }
    // );
    const ggl3 = L.tileLayer(
      "https://mt1.google.com/vt/lyrs=y&x={x}&y={y}&z={z}",
      {
        maxNativeZoom: 200,
        maxZoom: 200,
        attribution: '&copy; <a href="#">Google Map Hybrid</a>',
      }
    );
    // const ggl4 = L.tileLayer(
    //   "https://mt1.google.com/vt/lyrs=h&x={x}&y={y}&z={z}",
    //   {
    //     attribution: "Google",
    //   }
    // );
    // const ggl5 = L.tileLayer(
    //   "https://mt1.google.com/vt/lyrs=p&x={x}&y={y}&z={z}",
    //   {
    //     attribution: "Google",
    //   }
    // );
    // const ggl6 = L.tileLayer(
    //   "https://mt1.google.com/vt/lyrs=t&x={x}&y={y}&z={z}",
    //   {
    //     attribution: "Google",
    //   }
    // );

    // tiles = L.tileLayer(
    //   "https://cartodb-basemaps-{s}.global.ssl.fastly.net/dark_all/{z}/{x}/{y}.png",
    //   {
    //     // const tiles = L.tileLayer('https://tiles.stadiamaps.com/tiles/alidade_smooth_dark/{z}/{x}/{y}{r}.png', {
    //     // ตั้งค่า Zoom ได้แบบ ไม่มีจำกัด
    //     maxNativeZoom: 200,
    //     maxZoom: 200,
    //     attribution: '&copy; <a href="#">Developed By นายบัญชา ไวเปีย</a>',
    //   }
    // );





    // lat = 13, long = 100 zoom ครั้งแรกที่เรียกหน้าเว็บ
    const map = L.map("map", {
      // zoomDelta: 0.25, // ความละเอียดการ zoom
      // zoomSnap: 0, // ความละเอียดการ zoom
      // remove Zoom Control
      zoomControl: false,
      // layer
      layers: [ggl3],
    }).setView([13.680084, 100.539492], 9);

    // ปิดการแสดง Leaflet ที่มุมล่างขวา
    map.attributionControl.setPrefix(false);


    // Add วงกลม
    // var circle = L.circle([13.852747, 100.463344], {
    //   color: "red",
    //   fillColor: "#f03",
    //   fillOpacity: 0.5,
    //   radius: 1000,
    // }).addTo(map);

    // Add Polygon
    // var polygon = L.polygon(
    //   [
    //     [13.644652, 100.514209],
    //     [13.583256, 100.745167],
    //     [13.754059, 100.665432],
    //   ],
    //   { color: "red", fillColor: "#f03", fillOpacity: 0.1 }
    // ).addTo(map);

    // Add Popup
    //   map.on("click", function (e) {
    //     var popup = L.popup()
    //       .setLatLng(e.latlng)
    //       .setContent("You clicked the map at " + e.latlng.toString())
    //       .openOn(map);
    //   });

    // Add Tooltip
    // marker.bindTooltip("I am a marker.").openTooltip();

    // Add Circle Tooltip
    // circle.bindTooltip("I am a circle.").openTooltip();

    // Add Polygon Tooltip
    // polygon.bindTooltip("I am a polygon.").openTooltip();

    // get lat long
    // map.on("click", function (e) {
    //   var lat = e.latlng.lat;
    //   var lng = e.latlng.lng;
    //   console.log(lat, lng);
    // });

    // zoom to fly
    // map.on("click", function (e) {
    //     map.flyTo(e.latlng, 20, { duration: 3 });
    // });

    // zoom current
    // map.on("zoomend", function (e) {
    //   var zoom = map.getZoom();
    //   console.log(zoom);
    // });

    // event zoom
    // map.on("zoomend", function (e) {
    //   var zoom = map.getZoom();
    //   // console.log(zoom);
    // });

    // group layer
    baseMaps = {
      "Open Street Map": osm,
      // Google: ggl,
      // Google: ggl2,
      // Google3: ggl3,
      // Google4: ggl4,
      // Google5: ggl5,
      // Google6: ggl6,
      // Tiles: tiles,
    };

    // group layer leaflet-geoman
    overlayMaps = {
      // esri: esri,
      // Marker: marker,
      // Circle: circle,
      // Polygon: polygon
    };

    // เพิ่ม Layer Control มุมด้านขวาบนชั้น layer option คือ collapsed:true
    ctl_lry = {
      // show layer control
      collapsed: true,
      position: 'topleft'
    }
    layerControl = L.control.layers(baseMaps, overlayMaps, ctl_lry).addTo(map);

    // add base layer
    layerControl.addBaseLayer(ggl3, "Google Hybrid");
    // layerControl.addBaseLayer(ggl, "Google Map");


    // ตั้งค่าความสูง Layer การแสดงผล
    map.createPane('pane_level_deed_nonsv_dol').style.zIndex = 1; // แปลงโฉนด ยังไม่ได้สอบ
    map.createPane('mosaic').style.zIndex = 1; // ภาพถ่ายทางอากาศ
    map.createPane('pane_level_deed_sv_dol').style.zIndex = 2; // แปลงโฉนด สอบแล้ว
    map.createPane('geom_parcel_rent').style.zIndex = 3; // แปลงเช่า
    map.createPane('geom_parcel_building').style.zIndex = 4; // แปลงอาคาร
    map.createPane('geom_parcel_select').style.zIndex = 5; // แปลงที่เลือก มาจากค้นหา
    map.createPane('bind_popup').style.zIndex = 1000; // Popup ข้อความ
    // pane_level_legend
    map.createPane('pane_level_legend').style.zIndex = 1000; // แสดงข้อความ

    // add button tag a link position top right from html
    // var odm_3d = L.control({ position: "bottomright" });
    // odm_3d.onAdd = function (map) {
    //   // show button vertical
    //   var div = L.DomUtil.create("div", "odm_3d");
    //   // add icon 
    //   div.innerHTML += "<a class='btn btn-primary'  href='/editor_page/"+ feature.properties.id +"' target='_blank'/>สัญญาเช่า</a>";
    //   // add space
    //   div.innerHTML += '&nbsp;';
    //   // add icon
    //   // div.innerHTML += '<button type="button" class="btn btn-sm btn-primary" id="btn_3d">3D</button>';

    //   return div;
    // };
    // odm_3d.addTo(map);
    

    
    // var odm_3d2 = L.control({ position: "bottomright" });
    // odm_3d2.onAdd = function (map) {
    //   // show button vertical
    //   var div = L.DomUtil.create("div", "odm_3d2");
    //   div.innerHTML += '<button type="button" class="btn btn-sm btn-primary" id="btn_3d2">2D</button>';
    //   return div;
    // };
    // odm_3d2.addTo(map);

   

    


    // add legend and add pane_level_legend
    var legend = L.control({ position: "bottomleft" });
    legend.onAdd = function (map) {
      var div = L.DomUtil.create("div", "legend");
      // hearder legend h5
      div.innerHTML += '<h5>Legend</h5>';
      div.innerHTML += '<i style="background: #FF0042"></i><span>แปลงโฉนดที่ดิน สอบเขตแล้ว</span><br>';
      div.innerHTML += '<i style="background: #4600FF"></i><span>แปลงโฉนดที่ดิน ยังไม่ได้สอบเขต</span><br>';
      div.innerHTML += '<i style="background: #4AF70E"></i><span>แปลงเช่า</span><br>';
      div.innerHTML += '<i style="background: #00FFFF"></i><span>สิ่งปลูกสร้าง</span><br>';
      return div;
    };
    legend.addTo(map);
    
    
    
    
    
    // wms layer tile Drone
    // http://localhost:8001 split :8001
    var base_url_split = base_url.split(":8001"); // return http://localhost
    var lyr_geoserver_mosaic = L.tileLayer.wms(base_url_split[0] +":8081/geoserver/cpb/wms", {
      layers: "cpb:ortho",
      format: "image/png",
      transparent: true,
      tiled: true,
      maxNativeZoom: 200,
      maxZoom: 200,
      attribution: "ภาพถ่ายทางอากาศ",
    });
    // add layer control
    layerControl.addOverlay(lyr_geoserver_mosaic, "ภาพถ่ายทางอากาศ");

    // google search box ค้นหาสถานที่สำคัญ...
    // Define the Google Places Search Box control
    var GooglePlacesSearchBox = L.Control.extend({
      onAdd: function(map) {
        var element = document.createElement("input");
        element.id = "searchBox";
        element.type = "text";
        element.placeholder = "ค้นหาสถานที่สำคัญ...";
        // change style
        element.style.width = "300px";
        element.style.height = "50px";
        element.style.margin = "10px";
        element.style.padding = "5px";
        element.style.border = "1px solid #ccc";
        element.style.borderRadius = "4px";
        element.style.boxShadow = "0 2px 6px rgba(0, 0, 0, 0.3)";
        // text height
        element.style.fontSize = "16px";
        return element;
      },
      onRemove: function(map) {
        // Nothing to do here
      }
    });
    // Add the Google Places Search Box control to the map
    (new GooglePlacesSearchBox()).addTo(map);

    // Get the search box element
    var input = document.getElementById("searchBox");
    var searchBox = new google.maps.places.SearchBox(input);

    // clear search box when user clicks on it
    input.addEventListener("click", function() {
      input.value = "";
    });
    // Listen for the event fired when the user selects a prediction and retrieve
    // more details for that place.
    // more details for that place.
    searchBox.addListener("places_changed", function() {
      var places = searchBox.getPlaces();
      if (places.length == 0) {
        return;
      }

      // Clear out the old markers.
      var group = L.featureGroup();

      // For each place, get the icon, name and location.
      var bounds = new L.LatLngBounds();
      places.forEach(function(place) {
        if (!place.geometry) {
          console.log("Returned place contains no geometry");
          return;
        }

        // remove marker when search
        map.eachLayer(function(layer) {
          if (layer instanceof L.Marker) {
            map.removeLayer(layer);
          }
        });

        // Create a marker for each place.
        var marker = L.marker([
          place.geometry.location.lat(),
          place.geometry.location.lng()
        ]).bindPopup(place.name);
        group.addLayer(marker);

        if (place.geometry.viewport) {
          // Only geocodes have viewport.
          var viewport = [
            [place.geometry.viewport.getSouthWest().lat(), place.geometry.viewport.getSouthWest().lng()],
            [place.geometry.viewport.getNorthEast().lat(), place.geometry.viewport.getNorthEast().lng()]
          ];
          bounds.extend(viewport);
        } else {
          bounds.extend(marker.getLatLng());
        }
      });

      // Add the group of markers to the map and fit the map's bounds to include all markers
      group.addTo(map);
      if (bounds.isValid()) {
        map.fitBounds(bounds);
      } else {
        console.log('Bounds are not valid.');
      }
    });
    
    // End google search

    // search ajax แปลงโฉนดที่ดิน POST
    $("#search_ajax").keyup(function () {
      var search_text = $(this).val();
      if (search_text.length > 0) {
        // split คำที่ค้นหา
        var search_text_split = search_text.split(" ");
        $.ajax({
          url: "/search_ajax/" + search_text_split + "/",
          type: "GET",
          dataType: "json",
          data: {
            search: search_text,
          },
          success: function (data) {
            // show result deed
            if (data.deed.length > 0) {
              // console.log(data.deed);
              $('#search_parcel').html('');
              // loop data
              for (var i = 0; i < data.deed.length; i++) {
                show_deed_detail_search(
                  data.deed[i].deed_no,
                  data.deed[i].tam_code,
                  data.deed[i].amp_code,
                  data.deed[i].prov_code,
                  data.deed[i].rai_number,
                  data.deed[i].ngarn_number,
                  data.deed[i].sqw_number,
                  data.deed[i].parcel_deed_id
                );
                $('#search_parcel').append(html);
                // console.log(data.deed[i].deed_no);
                // show_deed_detail_search(data.deed[i].deed_no);
              }

            }

            // show result rent
            if (data.rent.length > 0) {
              console.log(data.rent.length);
            }
            // console.log(data);

            // $("#result").html(data);
          },
        });
      }
      // console.log(search_text);
    });
    // End search ajax แปลงโฉนดที่ดิน POST


    // Zoom แปลงที่ดิน จากการค้นหา
    $(document).on('click', '.id_parcel', function () {
      var id_parcel = $(this).attr('id');
      // split คำที่ค้นหา
      var id_parcel_split = id_parcel.split(" ");
      type = id_parcel_split[0];
      id_parcel = id_parcel_split[1];
      // console.log(id_parcel_split[0], id_parcel_split[1]);
      $.ajax({
        url: "/get_geom_search_id/" + type + "/" + id_parcel + "/",
        type: "GET",
        dataType: "json",
        success: function (data) {
          // check has layer
          // if(map.hasLayer(geojsonLayer)){
          //   map.removeLayer(geojsonLayer);
          // }
          try {
            map.removeLayer(select_layer_srh);
          }
          catch (err) {
            console.log(err);
          }
          console.log(data[1]);
          // ตรวจสอบว่าเป็นข้อมูลแปลงโฉนด
          if (data[1] == 'deed') {
            // get data json pase
            console.log(data);
            // add layer geojson
            select_layer_srh = L.geoJSON(data, {
              // get property
              pane: 'geom_parcel_select',
              // style: polygon select,
              style: function (feature) {
                return {
                  color: "#FFFB00",
                  weight: 4,
                  opacity: 0.65,
                  fillColor: "#FFFB00",
                  fillOpacity: 0.7,
                };
              },
              onEachFeature: function (feature, layer) {
                // zoom to fly layer
                // map.flyToBounds(layer.getBounds());
                map.fitBounds(layer.getBounds());
              },
            }).addTo(map);
          }
          // end if
        },
      });
    });
    // End Zoom แปลงที่ดิน จากการค้นหา


    // โหลดแปลงโฉนดที่ดิน ไม่สอบเขตโฉนด
    // style: polygon deed,
    style_polygon_deed_survey_dol_non = function (feature) {
      return {
        color: "#4600FF",
        weight: 4,
        opacity: 1,
        fillColor: "#4600FF",
        fillOpacity: 0,
        dashArray: '6',
      };
    };
    $.ajax({
      url: "/ajax_parcel_deed/False/",
      type: "GET",
      dataType: "json",
      success: function (data) {
        polygon_deed_nonsv_dol = L.geoJSON(data, {
          pane: 'pane_level_deed_nonsv_dol',
          // style: polygon deed,
          style: style_polygon_deed_survey_dol_non,
          onEachFeature: function (feature, layer) {

            layer.on('click', function (e) {
              // remove layer select
              try {
                if (map.hasLayer(select_layer_srh)) {
                  map.removeLayer(select_layer_srh);
                }
              } catch (error) {
                console.log(error);
              }
              // layer to geojson
              select_layer_srh = L.geoJSON(layer.toGeoJSON());
              // change style
              select_layer_srh.setStyle({
                color: "#FFFB00",
                weight: 2,
                opacity: 0.65,
                fillColor: "#FFFB00",
                fillOpacity: 0.7,
              });
              // add to map
              select_layer_srh.addTo(map);


              // console.log(feature.properties.id);
              $.ajax({
                url: "/ajax_detail_parcel_deed/" + feature.properties.id + "/",
                type: "GET",
                dataType: "json",
                success: function (data, status) {
                  // console.log(data);
                  // console.log(e);
                  // console.log(e.latlng.lng);
                  // layer.bindPopup("โฉนดที่ดินหมายเลข: " + data.deed_no + "</br>เลขที่ดิน: " + data.land_no + "").openPopup();
                  html = "<p>โฉนดที่ดินหมายเลข: " + data.deed_no +
                    "</br>เลขที่ดิน: " + data.land_no +
                    "</br>ตำบล: " + data.tam_code + " อำเภอ: " + data.amp_code + " จังหวัด: " + data.prov_code +
                    "</br>พื้นที่: " + data.rai_number + " ไร่ " + data.ngarn_number + " งาน " + data.sqw_number + " ตารางวา " +
                    // "</br>อยู่ในความดูแลของ: " + data.responsibility +
                    "</br><a class='btn btn-primary' href='https://www.google.co.th/maps/place/" + e.latlng.lat + "," + e.latlng.lng + "' target='_blank'>เส้นทาง</a>" +
                    "<span>   </span><a class='btn btn-primary' href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=" + e.latlng.lat + "," + e.latlng.lng + "' target='_blank'>Street View</a>";

                  var popup = L.popup({
                    pane: 'bind_popup',
                  }).setLatLng(e.latlng).setContent(html).openOn(map);
                }
              });
            });
            // console.log(feature.properties.id);
            // bind popup
            // layer.bindPopup("<p>Name: " + feature.properties.id + "</p>").openPopup();
          },
        });
        // add layer to group
        polygon_deed_nonsv_dol_group = L.layerGroup([polygon_deed_nonsv_dol]);
        // add layer to layer control checked box
        layerControl.addOverlay(polygon_deed_nonsv_dol_group, "แปลงโฉนดที่ดิน ยังไม่สอบเขต");
        // polygon_deed_group add to pane
        // polygon_deed_nonsv_dol_group.addTo(map);

      }
    });
    // End โหลดแปลงโฉนดที่ดิน ไม่สอบเขตโฉนด


    // โหลดแปลงโฉนดที่ดิน สอบเขตโฉนดแล้ว
    // style: polygon deed,
    style_polygon_deed_survey_dol = function (feature) {
      return {
        color: "#FF0042",
        weight: 2,
        opacity: 1,
        fillColor: "#FF0042",
        fillOpacity: 0,
      };
    };
    $.ajax({
      url: "/ajax_parcel_deed/True/",
      type: "GET",
      dataType: "json",
      success: function (data) {
        polygon_deed_sv_dol = L.geoJSON(data, {
          pane: 'pane_level_deed_sv_dol',
          // style: polygon deed,
          style: style_polygon_deed_survey_dol,
          onEachFeature: function (feature, layer) {

            layer.on('click', function (e) {
              // remove layer select
              try {
                if (map.hasLayer(select_layer_srh)) {
                  map.removeLayer(select_layer_srh);
                }
              } catch (error) {
                console.log(error);
              }
              // layer to geojson
              select_layer_srh = L.geoJSON(layer.toGeoJSON());
              // change style
              select_layer_srh.setStyle({
                color: "#FFFB00",
                weight: 2,
                opacity: 0.65,
                fillColor: "#FFFB00",
                fillOpacity: 0.7,
              });
              // add to map
              select_layer_srh.addTo(map);



              // console.log(feature.properties.id);
              $.ajax({
                url: "/ajax_detail_parcel_deed/" + feature.properties.id + "/",
                type: "GET",
                dataType: "json",
                success: function (data, status) {
                  // console.log(data);
                  // console.log(e);
                  // console.log(e.latlng.lng);
                  // layer.bindPopup("โฉนดที่ดินหมายเลข: " + data.deed_no + "</br>เลขที่ดิน: " + data.land_no + "").openPopup();
                  html = "<p>โฉนดที่ดินหมายเลข: " + data.deed_no +
                    "</br>เลขที่ดิน: " + data.land_no +
                    "</br>ตำบล: " + data.tam_code + " อำเภอ: " + data.amp_code + " จังหวัด: " + data.prov_code +
                    "</br>พื้นที่: " + data.rai_number + " ไร่ " + data.ngarn_number + " งาน " + data.sqw_number + " ตารางวา " +
                    // "</br>อยู่ในความดูแลของ: " + data.responsibility +
                    "</br><a class='btn btn-primary' href='https://www.google.co.th/maps/place/" + e.latlng.lat + "," + e.latlng.lng + "' target='_blank'>เส้นทาง</a>" +
                    "<span>   </span><a class='btn btn-primary' href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=" + e.latlng.lat + "," + e.latlng.lng + "' target='_blank'>Street View</a>";
                  var popup = L.popup({
                    pane: 'bind_popup',
                  }).setLatLng(e.latlng).setContent(html).openOn(map);
                }
              });
            });
            // console.log(feature.properties.id);
            // bind popup
            // layer.bindPopup("<p>Name: " + feature.properties.id + "</p>").openPopup();
          },
        });
        // add layer to group
        polygon_deed_sv_dol_group = L.layerGroup([polygon_deed_sv_dol]);
        // add layer to layer control checked box
        layerControl.addOverlay(polygon_deed_sv_dol_group, "แปลงโฉนดที่ดิน สอบเขตแล้ว");
        // polygon_deed_group add to pane
        polygon_deed_sv_dol_group.addTo(map);

      }
    });
    // End โหลดแปลงโฉนดที่ดิน สอบเขตโฉนด


    // โหลดแปลงเช่า
    // style polygon rent
    style_polygon_rent = function (feature) {
      return {
        color: "#4AF70E",
        weight: 2,
        opacity: 0.65,
        fillColor: "#4AF70E",
        fillOpacity: 0.1,
      };
    };
    $.ajax({
      url: "/ajax_parcel_rent/",
      type: "GET",
      dataType: "json",
      success: function (data) {
        layer_shp_rents = L.geoJSON(data, {
          pane: 'geom_parcel_rent',
          // style: polygon deed,
          style: style_polygon_rent,
          onEachFeature: function (feature, layer) {

            // get property id from geojson
            layer.on('click', function (e) {
              // remove layer select
              try {
                if (map.hasLayer(select_layer_srh)) {
                  map.removeLayer(select_layer_srh);
                }
              } catch (error) {
                console.log(error);
              }
              console.log(feature.properties.id);
              // layer to geojson
              select_layer_srh = L.geoJSON(layer.toGeoJSON());
              // change style
              select_layer_srh.setStyle({
                color: "#C70039",
                weight: 8,
                opacity: 1,
                fillColor: "#FFFB00",
                fillOpacity: 0,
              });
              // add to map
              select_layer_srh.addTo(map);

              //  console.log(feature.properties.id); 

              // console.log(feature.properties.id);
              $.ajax({
                url: "/ajax_detail_parcel_rents/" + feature.properties.id + "/",
                type: "GET",
                dataType: "json",
                success: function (data, status) {
                  // console.log(data);
                  // console.log(e);
                  // console.log(e.latlng.lng);
                  // layer.bindPopup("โฉนดที่ดินหมายเลข: " + data.deed_no + "</br>เลขที่ดิน: " + data.land_no + "").openPopup();
                  html = "<p>ที่ดินแปลงหมายเลข: " + data.number_parcel_rent +
                    "</br>ชื่อผู้เช่า: " + data.name_rent +
                    "</br>ตำบล: " + data.talk_tunbon + " อำเภอ: " + data.amphoe + " จังหวัด: " + data.province +
                    "</br>พื้นที่เช่าประมาณ: " + data.area_rent_sqwa + " ตารางวา " +
                    // "</br>อยู่ในความดูแลของ: " + data.responsibility +
                    "</br><a class='btn btn-primary' href='https://www.google.co.th/maps/place/" + e.latlng.lat + "," + e.latlng.lng + "' target='_blank'>เส้นทาง</a>" +
                    "<span>   </span><a class='btn btn-primary' href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=" + e.latlng.lat + "," + e.latlng.lng + "' target='_blank'>Street View</a>" +
                    "</br></br><a class='btn btn-primary'  href='/editor_page/"+ feature.properties.id +"' target='_blank'/>สัญญาเช่า</a>" ;
                  var popup = L.popup({
                    pane: 'bind_popup',
                  }).setLatLng(e.latlng).setContent(html).openOn(map);
                }
              });
              // End get property id from geojson
              var popup = L.popup({
                pane: 'bind_popup',
              }).setLatLng(e.latlng).setContent("<a class='btn btn-primary'  href='/editor_page/"+ feature.properties.id +"' target='_blank'/>สัญญาเช่า</a>").openOn(map);
            });

          },
        });
        // add layer to group
        polygon_rent_group = L.layerGroup([layer_shp_rents]);
        // add layer to layer control checked box
        layerControl.addOverlay(polygon_rent_group, "แปลงเช่า");
        polygon_rent_group.addTo(map);
      }
    });
    // End โหลดแปลงเช่า


    // โหลดสิ่งปลูกสร้าง
    $.ajax({
      url: "/ajax_building/",
      type: "GET",
      dataType: "json",
      success: function (data) {
        geojsonLayer = L.geoJSON(data, {
          pane: 'geom_parcel_building',
          // style: polygon deed,
          style: function (feature) {
            return {
              color: "#00FFFF",
              weight: 2,
              opacity: 0.65,
              fillColor: "#00FFFF",
              fillOpacity: 0.1,
            };
          },
        });
        // add layer to group
        polygon_building_group = L.layerGroup([geojsonLayer]);
        // add layer to layer control checked box
        layerControl.addOverlay(polygon_building_group, "สิ่งปลูกสร้าง");
        polygon_building_group.addTo(map);
      }
    });
    // End โหลดสิ่งปลูกสร้าง


    


    function show_deed_detail_search(deed_no, tam_code, amp_code, prov_code, rai_number, ngarn_number, sqw_number, parcel_deed_id) {
      html = '<div class="d-flex align-items-center mb-5">' +
        '<!--begin::Symbol-->' +
        '<div class="symbol symbol-40px me-4">' +
        '<span class="symbol-label bg-light">' +
        '<!--begin::Svg Icon | path: icons/duotune/electronics/elc004.svg-->' +
        '<span class="svg-icon svg-icon-2 svg-icon-primary">' +
        '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">' +
        '<path d="M2 16C2 16.6 2.4 17 3 17H21C21.6 17 22 16.6 22 16V15H2V16Z" fill="currentColor"></path>' +
        '<path opacity="0.3" d="M21 3H3C2.4 3 2 3.4 2 4V15H22V4C22 3.4 21.6 3 21 3Z" fill="currentColor"></path>' +
        '<path opacity="0.3" d="M15 17H9V20H15V17Z" fill="currentColor"></path>' +
        '</svg>' +
        '</span>' +
        '<!--end::Svg Icon-->' +
        '</span>' +
        '</div>' +
        '<!--end::Symbol-->' +
        '<!--begin::Title-->' +
        '<div class="d-flex flex-column">' +
        '<a href="#" id="deed ' + parcel_deed_id + '" class="id_parcel fs-6 text-gray-800 text-hover-primary fw-semibold">โฉนดที่ดินหมายเลข ' + deed_no + '</a>' +
        '<span class="fs-7 text-muted fw-semibold">ต.' + tam_code + ' อ.' + amp_code + ' จ.' + prov_code + '</span>' +
        '<span class="fs-7 text-muted fw-semibold">พื้นที่ ' + rai_number + ' ไร่ ' + ngarn_number + ' งาน ' + sqw_number + ' ตารางวา</span>' +
        '</div>' +
        '<!--end::Title-->' +
        '</div>';
    }
  });
</script>
{% endblock js %}
